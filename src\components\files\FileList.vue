<script setup lang="ts">
import { ref, onMounted, watch, computed } from 'vue';
import { listRequestFiles, deleteRequestFile, downloadRequestFile, type RequestFileItem } from '@/lib/api/index';
import { useI18n } from 'vue-i18n';

const props = defineProps<{ requestId: number | string; isEditMode?: boolean }>();
const emit = defineEmits<{ (e: 'deleted'): void }>();

const files = ref<RequestFileItem[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);

const { t } = useI18n();

const requestIdNum = computed(() => Number(props.requestId));

const fetchFiles = async () => {
  loading.value = true;
  error.value = null;
  try {
    const { data } = await listRequestFiles(requestIdNum.value);
    files.value = data as any;
  } catch (e) {
    console.error('Failed to load files', e);
    error.value = t('Failed to load files');
  } finally {
    loading.value = false;
  }
};

onMounted(fetchFiles);
watch(() => props.requestId, () => fetchFiles());

const openPreview = ref(false);
const previewSrc = ref<string | null>(null);
const previewName = ref<string | null>(null);

const mimeByExtension: Record<string, string> = {
  pdf: 'application/pdf',
  doc: 'application/msword',
  docx: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  xls: 'application/vnd.ms-excel',
  xlsx: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  csv: 'text/csv',
  jpg: 'image/jpeg',
  jpeg: 'image/jpeg',
  png: 'image/png',
  gif: 'image/gif',
  webp: 'image/webp',
  bmp: 'image/bmp',
  svg: 'image/svg+xml'
};

const getMimeFromExtension = (ext?: string) => {
  if (!ext) return 'application/octet-stream';
  const key = ext.toLowerCase().replace(/^\./, '');
  return mimeByExtension[key] || 'application/octet-stream';
};

const isPreviewable = (ext?: string) => {
  const mime = getMimeFromExtension(ext);
  return mime === 'application/pdf' || mime.startsWith('image/');
};

const isCurrentPdf = computed(() => {
  if (!previewName.value) return false;
  const f = files.value.find(f => f.name === previewName.value);
  const mime = getMimeFromExtension(f?.extension);
  return mime === 'application/pdf';
});

const handleFileClick = async (file: RequestFileItem) => {
  try {
    const { data } = await downloadRequestFile(requestIdNum.value, file.docId);
    const mime = getMimeFromExtension(file.extension);
    const blob = new Blob([data], { type: mime });
    const url = URL.createObjectURL(blob);
    if (isPreviewable(file.extension)) {
      previewSrc.value = url;
      previewName.value = file.name;
      openPreview.value = true;
    } else {
      // trigger download for non-previewable types
      const a = document.createElement('a');
      a.href = url;
      a.download = file.name;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);
    }
  } catch (e) {
    console.error('Failed to download file', e);
    alert(t('Failed to download file'));
  }
};

const downloadCurrent = async () => {
  if (!previewSrc.value || !previewName.value) return;
  const a = document.createElement('a');
  a.href = previewSrc.value;
  a.download = previewName.value;
  document.body.appendChild(a);
  a.click();
  document.body.removeChild(a);
};

const confirmAndDelete = async (file: RequestFileItem) => {
  if (!props.isEditMode) return;
  if (!confirm(t('Are you sure you want to delete this file?') as string)) return;
  try {
    await deleteRequestFile(requestIdNum.value,file.docId);
    await fetchFiles();
    emit('deleted');
  } catch (e) {
    console.error('Failed to delete file', e);
    alert(t('Failed to delete file'));
  }
};
</script>

<template>
  <div>
    <div class="d-flex align-center mb-2">
      <h3 class="text-subtitle-1 mb-0">Files</h3>
      <v-spacer />
      <v-btn icon="refresh" variant="text" @click="fetchFiles" :disabled="loading" />
    </div>

    <v-alert v-if="error" type="error" class="mb-2">{{ error }}</v-alert>

    <v-skeleton-loader v-if="loading" type="table" />

    <v-table v-else density="compact">
      <thead>
        <tr>
          <th class="text-left">File Name</th>
          <!-- <th class="text-left" style="width: 160px;">Uploaded At</th>
          <th class="text-left" style="width: 160px;">Uploaded By</th> -->
          <th v-if="props.isEditMode" class="text-left" style="width: 80px;">Actions</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="file in files" :key="file.docId">
          <td>
            <a href="#" @click.prevent="handleFileClick(file)">{{ file.name }}</a>
          </td>
          <!-- <td>{{ file.uploadedAt || '-' }}</td>
          <td>{{ file.uploadedBy || '-' }}</td> -->
          <td v-if="props.isEditMode">
            <v-btn color="error" variant="text" icon="delete" @click="confirmAndDelete(file)" />
          </td>
        </tr>
        <tr v-if="!files || files.length === 0">
          <td :colspan="props.isEditMode ? 4 : 3" class="text-center text-medium-emphasis">No files uploaded</td>
        </tr>
      </tbody>
    </v-table>
    <v-dialog v-model="openPreview" max-width="900">
      <v-card>
        <v-card-title class="d-flex align-center">
          <span class="text-subtitle-1">{{ previewName }}</span>
          <v-spacer />
          <v-btn icon="download" variant="text" @click="downloadCurrent" />
          <v-btn icon="close" variant="text" @click="openPreview=false" />
        </v-card-title>
        <v-divider />
        <v-card-text style="min-height: 400px;">
          <div v-if="previewSrc && files.length">
            <iframe
              v-if="isCurrentPdf"
              :src="previewSrc"
              style="width: 100%; height: 70vh; border: none;"
            />
            <img
              v-else
              :src="previewSrc"
              :alt="previewName || ''"
              style="max-width: 100%; max-height: 70vh; display: block; margin: 0 auto;"
            />
          </div>
          <div v-else class="text-medium-emphasis">No preview available</div>
        </v-card-text>
      </v-card>
    </v-dialog>
  </div>
</template>

<style scoped>
</style>

