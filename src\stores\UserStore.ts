/**
 * @file Pinia store defining user state for the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { defineStore } from 'pinia';
import type { UserStoreState } from '@/lib/common/types';
import type { MSGraphUser } from '@/lib/common/types';

/**
 * ------
 * Export
 * ------
 */

export const useUserStore = defineStore( 'UserStore',
{
    /**
	 * Store state variables.
	 */
    state : () : UserStoreState =>
    {
        return {
            authenticated: false,
            microsoftID: null,
            employeeID: null,
            userPrincipalName: null,
            emailAddress: null,
            givenName: null,
            surName: null,
            displayName: null,
            jobTitle: null,
            department: null,
            companyName: null,
            officeStreet: null,
            officeCity: null,
            officeState: null,
            officePostalCode: null,
            officeCountry: null,
            phoneMobile: null,
            phoneFax: null,
            businessPhones: null,
            manager: null,
            photoURL: null
        }
    },

    /**
	 * Store actions (same as methods).
	 */
    actions :
    {
        /**
         * Sets the user store state variables given the properties passed.
         * 
         * @param { any } [userDetails] - Login Refresh returned data value (POST).
         */
        setUser ( user : MSGraphUser, photoURL? : string )
        {
            this.microsoftID = user.microsoftID;
            this.employeeID = user.employeeID,
            this.userPrincipalName = user.userPrincipalName;
            this.emailAddress = user.emailAddress;
            this.givenName = user.givenName;
            this.surName = user.surName;
            this.displayName = user.displayName;
            this.jobTitle = user.jobTitle;
            this.department = user.department;
            this.companyName = user.companyName;
            this.officeStreet = user.officeStreet;
            this.officeCity = user.officeCity;
            this.officeState = user.officeState;
            this.officePostalCode = user.officePostalCode;
            this.officeCountry = user.officeCountry;
            this.phoneMobile = user.phoneMobile;
            this.phoneFax = user.phoneFax;
            this.businessPhones = user.businessPhones;
            this.manager = user.manager;
            this.photoURL = photoURL || null;

            this.authenticated = true;
        },

        /**
         * Sets user photo URL.
         * 
         * @param { string } [photoURL] - URL to photo.
         */
        setPhoto ( photoURL : string )
        {
            this.photoURL = photoURL;           
        },

        /**
         * Clears user data from the store.
         */
        clearUser ()
        {
            this.authenticated = false;

            this.microsoftID = null;
            this.employeeID = null;
            this.userPrincipalName = null;
            this.emailAddress = null;
            this.givenName = null;
            this.surName = null;
            this.displayName = null;
            this.jobTitle = null;
            this.department = null;
            this.companyName = null;
            this.officeStreet = null;
            this.officeCity = null;
            this.officeState = null;
            this.officePostalCode = null;
            this.officeCountry = null;
            this.phoneMobile = null;
            this.phoneFax = null;
            this.businessPhones = null;
            this.manager = null;
            this.photoURL = null;
        }
    }
}); 