<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch } from 'vue';

// Type Definitions
type RateType = 'bw' | 'colour' | 'iprc' | 'minimum_base_amt' | 'minimum_base_volume';
type Discounts = Record<RateType, number | null>;
type PublishedRates = Record<RateType, number | null>; // includes iprc

interface ServiceOption {
  id: string;
  fixed_service_term_length: number | null;
  bw: number | null;
  colour: number | null;
  iprc: number | null;
  minimum_base_amt: number | null;
  minimum_base_volume: number | null;
}

interface Product {
  id?: number | string;
  model: string | null;
  // Backend published rate fields
  blackAndWhite?: number | null;
  colorValue?: number | null;
  oversizedValue?: number | null;
  minimumBase?: number | null;
  minimumVolume?: number | null;

  dsd_qty: number | null;
  dealer_qty: number | null;
  estimated_amv_unit: number | null;
  colour_percentage: number | null;
  oversize_percentage: number | null;
  fees_included_in_cpc: boolean;
  // published_rates is removed from here, will be computed
  discounts: Discounts;
  serviceOptions: ServiceOption[];
}

// Props and Emits
const props = defineProps<{
  modelValue: Product;
  selectedServiceValuePack: string;
  defaultTermLength: number;
}>();

const emit = defineEmits(['update:modelValue']);

const localProductData = computed({
  get: () => props.modelValue,
  set: (value: Product) => emit('update:modelValue', value)
});

watch(() => props.defaultTermLength, (newTermLength) => {
  if (localProductData.value.serviceOptions && localProductData.value.serviceOptions.length > 0) {
    localProductData.value.serviceOptions[0].fixed_service_term_length = newTermLength;
  }
});

// --- DATA --- //




const termOptions = [
  { title: '12 Months', value: 12 },
  { title: '24 Months', value: 24 },
  { title: '36 Months', value: 36 },
  { title: '48 Months', value: 48 },
  { title: '60 Months', value: 60 },
];

// --- COMPUTED PROPERTIES FOR DYNAMIC RATES --- //
const adjustedPublishedRates = computed<PublishedRates>(() => {
  const product: any = localProductData.value;
  let baseRates: PublishedRates = {
    bw: product.blackAndWhite ?? null,
    colour: product.colorValue ?? null,
    iprc: product.iprc ?? null,
    minimum_base_amt: product.minimumBase ?? null,
    minimum_base_volume: product.minimumVolume ?? null,
  };

  return baseRates;
});

// Calculate discounts based on the FIRST service option (index 0)
const calculateDiscountsFromFirstOption = () => {
  if (!localProductData.value.serviceOptions || localProductData.value.serviceOptions.length === 0) return;
  const published = adjustedPublishedRates.value;
  const firstOption = localProductData.value.serviceOptions[0];
  if (!published || !firstOption) return;

  const newDiscounts: Discounts = { bw: null, colour: null, iprc: null, minimum_base_amt: null, minimum_base_volume: null };
  (Object.keys(published) as RateType[]).forEach(rt => {
    const pub = published[rt];
    const req = firstOption[rt];
    if (pub === null || req === null || pub === 0) {
      newDiscounts[rt] = null;
    } else {
      const disc = (1 - req / pub) * 100;
      newDiscounts[rt] = parseFloat(disc.toFixed(2));
    }
  });
  localProductData.value.discounts = newDiscounts;
};


// --- METHODS --- //
function createDefaultServiceOption(): ServiceOption {
  const newOption: ServiceOption = {
    id: `opt_${Date.now()}_${Math.random()}`,
                fixed_service_term_length: props.defaultTermLength,
    bw: null,
    colour: null,
    iprc: null,
    minimum_base_amt: null,
    minimum_base_volume: null
  };
  // Immediately calculate rates for the new option
  calculateAllRatesForOption(newOption);
  return newOption;
}

function addServiceOption() {
  if (localProductData.value.serviceOptions.length >= 4) return;
  const newOption = createDefaultServiceOption();
  localProductData.value.serviceOptions.push(newOption);
}

function removeServiceOption(optionId: string) {
  if (localProductData.value.serviceOptions.length <= 1) return;
  const index = localProductData.value.serviceOptions.findIndex(opt => opt.id === optionId);
  if (index > -1) {
    localProductData.value.serviceOptions.splice(index, 1);
  }
}

const calculateRequestedRate = (baseRate: number | null, discount: number | null): number | null => {
  if (baseRate === null || discount === null) return null;
  const rate = baseRate * (1 - (discount / 100));
  return parseFloat(rate.toFixed(5));
};

function calculateAllRatesForOption(option: ServiceOption) {
  const published = adjustedPublishedRates.value;
  if (!published) return;

  const discounts = localProductData.value.discounts;

  (Object.keys(published) as RateType[]).forEach(rateType => {
    option[rateType] = calculateRequestedRate(published[rateType], discounts[rateType]);
  });
}

// This method is kept for potential future use (e.g., seed requested rates based on default discounts)
function updateAllOptions() {
  localProductData.value.serviceOptions.forEach(calculateAllRatesForOption);
}

// INITIAL calculation of discounts
calculateDiscountsFromFirstOption();

// --- INITIALIZATION --- //
if (!localProductData.value.serviceOptions) {
  localProductData.value.serviceOptions = [];
}
if (localProductData.value.serviceOptions.length === 0) {
  addServiceOption(); // Add one default option if none exist
}

// --- WATCHERS --- //
// Watch the computed property. When it changes, update all options.
watch(adjustedPublishedRates, () => {
  // Recalculate discounts whenever published rates change
  calculateDiscountsFromFirstOption();
}, { immediate: true });

// Watch for changes in the FIRST service option to update discounts.
watch(() => localProductData.value.serviceOptions && localProductData.value.serviceOptions[0], () => {
  calculateDiscountsFromFirstOption();
}, { deep: true });

// --- FORMATTERS & RULES --- //
const requiredRule = (value: any) => !!value || 'This field is required.';
const formatRate = (value: number | null): string => value !== null ? value.toFixed(5).replace(/\.?0+$/, '') : '-';
const formatCurrency = (value: number | null): string => value !== null ? `$${value.toFixed(2)}` : '-';
const formatVolume = (value: number | null): string => value !== null ? value.toLocaleString() : '-';
const formatPercentage = (value: number | null): string => (value !== null && !isNaN(value)) ? `${value.toFixed(2)}%` : '-';

</script>

<template>
  <v-container fluid class="pa-0">
    <!-- I. Models Overview -->
    <v-card variant="tonal" class="mb-3">
      <v-card-title class="text-subtitle-2">Models Overview</v-card-title>
      <v-card-text>
        <v-row dense>
          <v-col cols="12" md="6">
            <v-text-field
              v-model="localProductData.model"
              label="Model"
              density="compact"
              readonly
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.dsd_qty" 
              label="DSD Qty" 
              type="number" 
              density="compact" 
              readonly
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.dealer_qty" 
              label="Dealer QTY" 
              type="number" 
              density="compact" 
              readonly
            />
          </v-col>
        </v-row>
        <v-row dense>
          <v-col cols="12" md="4">
            <v-text-field 
              v-model.number="localProductData.estimated_amv_unit" 
              label="Estimated AMV / Unit *" 
              type="number" 
              density="compact" 
              :rules="[requiredRule]" 
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.colour_percentage" 
              :label="adjustedPublishedRates.colour ? 'Colour % *' : 'Colour %'" 
              type="number" 
              suffix="%" 
              density="compact" 
              :rules="adjustedPublishedRates.colour ? [requiredRule] : []"
            />
          </v-col>
          <v-col cols="6" md="2">
            <v-text-field 
              v-model.number="localProductData.oversize_percentage" 
              :label="adjustedPublishedRates.iprc ? 'Oversize % *' : 'Oversize %'" 
              type="number" 
              suffix="%" 
              density="compact" 
              :rules="adjustedPublishedRates.iprc ? [requiredRule] : []"
            />
          </v-col>
          <v-col cols="12" md="2" class="d-flex align-center">
            <v-checkbox 
              v-model="localProductData.fees_included_in_cpc" 
              label="Fees Included in CPC" 
              density="compact" 
              hide-details 
            />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <!-- II. Published Rates with Discounts -->
    <v-card variant="tonal" class="mb-3">
      <v-card-title class="text-subtitle-2">Published Open Rates &amp; Discounts</v-card-title>
<v-card-subtitle class="text-caption mb-2">*Discount % is calculated from the first requested rate option and cannot be edited directly.</v-card-subtitle>
      <v-card-text>
        <v-table density="compact" v-if="localProductData.model && adjustedPublishedRates">
          <thead>
            <tr>
              <th class="text-left" style="width: 25%">Rate Type</th>
              <th class="text-right" style="width: 35%; padding-right: 16px">Published Open Rates</th>
              <th class="text-right" style="width: 40%">Discount %</th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td class="text-left font-weight-medium">B&W</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatRate(adjustedPublishedRates.bw) }}</span>
              </td>
              <td class="pl-4 text-right">
                <span class="text-h6 font-weight-bold">{{ formatPercentage(localProductData.discounts.bw) }}</span>
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Colour</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatRate(adjustedPublishedRates.colour) }}</span>
              </td>
              <td class="pl-4 text-right">
                <span class="text-h6 font-weight-bold">{{ formatPercentage(localProductData.discounts.colour) }}</span>
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Extra Long (IPRC)</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatRate(adjustedPublishedRates.iprc) }}</span>
              </td>
              <td class="pl-4 text-right">
                <span class="text-h6 font-weight-bold">{{ formatPercentage(localProductData.discounts.iprc) }}</span>
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Min. Base $</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatCurrency(adjustedPublishedRates.minimum_base_amt) }}</span>
              </td>
              <td class="pl-4 text-right">
                <span class="text-h6 font-weight-bold">{{ formatPercentage(localProductData.discounts.minimum_base_amt) }}</span>
              </td>
            </tr>
            <tr>
              <td class="text-left font-weight-medium">Min. Base Vol.</td>
              <td class="text-right pr-4">
                <span class="text-h6 font-weight-bold">{{ formatVolume(adjustedPublishedRates.minimum_base_volume) }}</span>
              </td>
              <td class="pl-4 text-right">
                <span class="text-h6 font-weight-bold">{{ formatPercentage(localProductData.discounts.minimum_base_volume) }}</span>
              </td>
            </tr>
          </tbody>
        </v-table>
        <p v-else class="text-caption grey--text">Select a model to view its published rates.</p>
      </v-card-text>
    </v-card>

    <!-- III. Service Options -->
    <v-card variant="tonal">
      <v-card-title class="text-subtitle-2 d-flex justify-space-between align-center">
        <span>Requested Rates</span>
        <v-btn size="small" color="primary" @click="addServiceOption" :disabled="localProductData.serviceOptions.length >= 4">
          Add Option
        </v-btn>
      </v-card-title>
      <v-card-text>
        <div v-if="!localProductData.model" class="text-center grey--text py-4">Select a model to configure service options.</div>
        <v-table v-else density="compact">
          <thead>
            <tr>
              <th class="text-left">Term</th>
              <th class="text-right">B&W Rate</th>
              <th class="text-right">Colour Rate</th>
              <th class="text-right">Extra Long (IPRC)</th>
              <th class="text-right">Min. Base Chg.</th>
              <th class="text-right">Min. Base Vol.</th>
              <th class="text-center">Actions</th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(option, index) in localProductData.serviceOptions" :key="option.id">
              <td style="width: 150px;">
                <v-select
                  v-model="option.fixed_service_term_length"
                  :items="termOptions"
                  density="compact"
                  hide-details
                  variant="outlined"
                ></v-select>
              </td>
              <td>
                <v-text-field
                  v-model.number="option.bw"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.00001"
                  min="0"
                />
              </td>
              <td>
                <v-text-field
                  v-model.number="option.colour"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.00001"
                  min="0"
                  :disabled="!adjustedPublishedRates.colour"
                />
              </td>
              <td>
                <v-text-field
                  v-model.number="option.iprc"
                  density="compact"
                  hide-details
                  type="number"
                  step="0.00001"
                  min="0"
                  :disabled="!adjustedPublishedRates.iprc"
                />
              </td>
              <td>
                <v-text-field
                  v-model.number="option.minimum_base_amt"
                  density="compact"
                  hide-details
                  prefix="$"
                  type="number"
                  step="0.01"
                  min="0"
                />
              </td>
              <td>
                <v-text-field
                  v-model.number="option.minimum_base_volume"
                  density="compact"
                  hide-details
                  type="number"
                  step="1"
                  min="0"
                />
              </td>
              <td class="text-center">
                <v-btn icon variant="text" size="small" @click="removeServiceOption(option.id)" :disabled="localProductData.serviceOptions.length <= 1">
                  <v-icon>delete</v-icon>
                </v-btn>
              </td>
            </tr>
          </tbody>
        </v-table>
      </v-card-text>
    </v-card>

  </v-container>
</template>

<style scoped>
/* Add any component-specific styles here if needed */
.v-table {
  --v-table-header-height: 40px;
}

.v-table th {
  font-weight: 600;
  letter-spacing: 0.3px;
}

.v-table td {
  padding: 0 8px;
  height: 52px;
}

.v-table .v-input {
  font-size: 0.9rem;
}

.v-table .v-input__control {
  height: 40px;
}
</style>
