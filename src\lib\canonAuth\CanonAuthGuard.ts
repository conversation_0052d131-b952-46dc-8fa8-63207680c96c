/**
 * @file Canon authentication guard for Vue Router.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { Router, RouteLocationNormalized } from 'vue-router';
import type { ICanonAuthGuard } from '@/lib/common/types';

import { CanonAuth } from './CanonAuth';
import { useAppStore } from "@/stores/AppStore";
import { useMessageStore } from '../../stores/MessageStore';
import { MessageType } from '@/lib/common/types';
import { CanonAuthState, RoleEnforcement } from '@/lib/common/types';
import useSetAuthState from '@/composables/auth/setAuthState';
import useUserLoginProcess from "@/composables/auth/userLoginProcess";

/**
 * ----
 * Main
 * ----
*/

const verifyRoles = ( to : RouteLocationNormalized , from : RouteLocationNormalized, i18n : any ) : boolean =>
{
    const appStore = useAppStore();

    // There are no roles required for the route.
    if ( !to.meta.roles )
    {
        appStore.stopLoader( 'SetAuthState' );

        return true;
    }
    // Roles are required for the route.
    else
    {
        // Check if user has all roles required.
        if ( ( !to.meta.roleEnforcement || to.meta.roleEnforcement === RoleEnforcement.ALL ) && CanonAuth.hasAllRoles( to.meta.roles as string[] ) )
        {
            // Necessary to stop the loader since a new page will not load.
            appStore.stopPageLoader();

            // Stop loader.
            appStore.stopLoader( 'SetAuthState' );

            return true;
        }
        // Check if user has any of the roles.
        else if ( to.meta.roleEnforcement === RoleEnforcement.ANY && CanonAuth.hasAnyRoles( to.meta.roles as string[] ) )
        {
            // Necessary to stop the loader since a new page will not load.
            appStore.stopPageLoader();

            // Stop loader.
            appStore.stopLoader( 'SetAuthState' );

            return true;
        }
        // User does not have roles required.
        else
        {
            const messageStore = useMessageStore();

            // User was on an existing route and clicked a secure page they don't have access to.
            // This denies the route loading and leaves them on their previous page.
            if ( from.name )
            {
                messageStore.show
                ({
                    type: MessageType.ERROR,
                    icon: 'lock',
                    title: i18n.global.t( 'app.auth.error.title_1' ),
                    body: i18n.global.t( 'app.auth.error.desc_1' ),
                    showContactIT: false,
                    btnClose: true,
                    btnRefresh: false,
                    disableApp: false
                });
            }
            // User did not have the app previously loaded and came to the page from an external source (link).
            // This denies the route, disables the app and does not allow user to close this message.
            else
            {
                messageStore.show
                ({
                    type: MessageType.ERROR,
                    icon: 'lock',
                    title: i18n.global.t( 'app.auth.error.title_1' ),
                    body: i18n.global.t( 'app.auth.error.desc_1' ),
                    showContactIT: false,
                    btnClose: false,
                    btnRefresh: false,
                    disableApp: true,
                    btnCustom     :
                    [
                        {
                            text: i18n.global.t( 'app.auth.button.go_home.label' ),
                            colour: 'primary',
                            callback: () =>
                            {
                                window.history.pushState( 'home', '', '/' );
                                document.location.reload();
                            },
                            close: false
                        }
                    ],
                });
            }

            // Necessary to stop the loader since a new page will not load.
            appStore.stopPageLoader();

            // Stop loader.
            appStore.stopLoader( 'SetAuthState' );
        }
    }

    return false;
}

/**
 * ----
 * Export
 * ----
*/

export const CanonAuthGuard : ICanonAuthGuard = 
{
    // Vue-Router plugin initializer.
    install : ( vueRouter : Router, i18n : any ) =>
    {
        // Add check before each route is loaded.
        vueRouter.beforeEach( async ( to : RouteLocationNormalized, from : RouteLocationNormalized ) =>
        {
            // Add language support.

            // Don't guard page's that don't exist.
            if ( to.name == '404' )
            {
                return true;
            }

            const appStore = useAppStore();

            // If the authentication state has not yet been set, do that first.
            if ( CanonAuth.authState === CanonAuthState.READY )
            {
                appStore.startLoader( 'SetAuthState' );

                // Register set auth state.
                const setAuthState = useSetAuthState;

                // Sets the user login state for the application.
                await setAuthState();
            }

            // Public Route - No authentication required.
            if ( !to.meta.secure )
            {
                if ( CanonAuth.authState === CanonAuthState.NO_ACCESS )
                {
                    const messageStore = useMessageStore();

                    messageStore.show
                    ({
                        type: MessageType.ERROR,
                        icon: 'lock',
                        title: i18n.global.t( 'app.auth.error.title_3' ),
                        body: i18n.global.t( 'app.auth.error.desc_3' ),
                        showContactIT: false,
                        btnClose: true,
                        btnRefresh: false,
                        disableApp: true
                    });
                }
                
                appStore.stopLoader( 'SetAuthState' );

                return true;
            }
            // Secure Route - Check for authentication.
            else
            {
                // User is authenticated (logged in).
                if ( CanonAuth.authState === CanonAuthState.IN_SESSION && CanonAuth.isAuthenticated() === true )
                {
                    return verifyRoles( to, from, i18n );
                }
                // User is not authenticated (logged out).
                else
                {
                    try
                    {
                        // Register login
                        const startLoginProcess = useUserLoginProcess;

                        // Send user to login.
                        const loginResult = await startLoginProcess( i18n.global.t );

                        // This code will only execute if the user was logged in via SSO (not redirect).
                        if ( loginResult )
                        {
                            return verifyRoles( to, from, i18n );
                        }
                    }
                    catch ( err )
                    {
                        const messageStore = useMessageStore();

                        messageStore.show
                        ({
                            type: MessageType.ERROR,
                            icon: 'lock',
                            title: i18n.global.t( 'app.auth.error.title_1' ),
                            body: i18n.global.t( 'app.auth.error.desc_1' ),
                            showContactIT: false,
                            btnClose: false,
                            btnRefresh: false,
                            disableApp: true
                        });
                    }
                }
            }
            
            // Stop the loader.
            appStore.stopLoader( 'SetAuthState' );

            return false;
        });
    }
}