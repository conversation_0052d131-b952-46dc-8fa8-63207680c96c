<script setup lang="ts">
/**
 * @file Reusable data table component.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';

/**
 * ----
 * Props
 * ----
 */
const props = defineProps({
    headers: {
        type: Array,
        required: true
    },
    items: {
        type: Array,
        required: true
    },
    loading: {
        type: Boolean,
        default: false
    },
    itemsPerPage: {
        type: Number,
        default: 25
    }
});

/**
 * ------
 * Emits
 * ------
 */
const emit = defineEmits(['action']);

/**
 * ----
 * Main
 * ----
 */
// Add language support
const { t } = useI18n();

// Handle action button click
const handleAction = (action: string, item: any) => {
    emit('action', { action, item });
};
</script>

<template>
    <v-data-table
        :headers="headers as any[]"
        :items="items"
        :loading="loading"
        :items-per-page="itemsPerPage"
        class="elevation-1"
    >
        <template v-for="(_, name) in $slots" v-slot:[name]="slotData">
            <slot :name="name" v-bind="slotData"></slot>
        </template>
    </v-data-table>
</template>
