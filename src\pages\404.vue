<script setup lang="ts">
	/**
	 * @file Error page (404).
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';
    import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
    const appStore = useAppStore();
    
    // Add language support.
    const { t } = useI18n();

    onMounted ( () =>
    {
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container fluid class="pt-8 viewport-centre">
        <v-row class="text-center">
            <v-col cols="12">
                <v-icon class="material-symbols-outlined" color="primary" size="96" icon="breaking_news"></v-icon>
            </v-col>
        </v-row>
		<v-row class="text-center">
			<v-col cols="12">
				<span class="text-h2">{{ t( 'page.404.header' ) }}</span>
				<br /><br />
                <span class="text-body-1">{{ t( 'page.404.body' ) }}</span>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>