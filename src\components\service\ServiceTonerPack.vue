<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  termOptions: {
    type: Array as () => { title: string; value: number }[],
    required: true,
  },
  modelValue: {
    type: Object,
    required: true,
    default: () => ({
      toner_in_out: null,
      service_value_pack: null,
    }),
  },
});

const emit = defineEmits(['update:modelValue']);

const localData = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const tonerOptions = ['Toner IN', 'Toner OUT'];
import { ref, onMounted } from 'vue';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';

// Dynamic service pack options fetched from LOV
const servicePackOptions = ref<{ title: string; value: string }[]>([]);

onMounted(async () => {
  try {
    const lovItems: LovItem[] = await getLov(LovCategories.SERVICE_VALUE_PACK);
    servicePackOptions.value = lovItems.map((l) => ({ title: l.value, value: l.lookupCode }));
    // If no value selected yet, default to first option
    if (!localData.value.service_value_pack && servicePackOptions.value.length) {
      localData.value.service_value_pack = servicePackOptions.value[0].value;
    }
  } catch (e) {
    console.error('Failed to load service value packs', e);
  }
});

const requiredRule = [(value: string | null) => !!value || 'This field is required. Please make a selection.'];

</script>

<template>
  <v-card class="mb-4">
    <v-card-title>Toner and Service Value Pack</v-card-title>
    <v-card-text>
      <v-row>
        <v-col cols="12" md="6">
          <v-select
            v-model="localData.toner_in_out"
            :items="tonerOptions"
            :rules="requiredRule"
            required
          >
            <template #label>
              Toner In/Out <span style="color:red">*</span>
            </template>
          </v-select>
        </v-col>
   
        <v-col cols="12" md="6">
          <v-select
            v-model="localData.service_value_pack"
            :items="servicePackOptions"
            :rules="requiredRule"
            required
            hint="This selection impacts calculated rates"
            persistent-hint
          >
            <template #label>
              Service Value Pack <span style="color:red">*</span>
            </template>
          </v-select>
        </v-col>
        <v-col cols="12" md="6">
          <v-select
            v-model="localData.default_term_length"
            :items="props.termOptions"
            label="Service Term Length"
            density="compact"
            variant="outlined"
            hide-details
          ></v-select>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
</style>
