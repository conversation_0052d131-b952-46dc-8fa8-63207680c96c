<script setup lang="ts">
import { ref, computed } from 'vue';
import { uploadRequestFile } from '@/lib/api/index';
import { useI18n } from 'vue-i18n';

const props = defineProps<{ requestId: number | string }>();
const emit = defineEmits<{ (e: 'uploaded'): void }>();

const selectedFile = ref<File | null>(null);
const isUploading = ref(false);

const { t } = useI18n();
const allowedFileTypes = [
  "application/pdf",
  "application/msword",                          // .doc
  "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
  "application/vnd.ms-excel",                    // .xls
  "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // .xlsx
  "text/csv",                                    // .csv
  "image/jpeg",                                  // .jpg, .jpeg
  "image/png"                                    // .png
];

// File extension fallback (in case MIME is not reliable)
const allowedExtensions = ['pdf', 'doc', 'docx', 'xls', 'xlsx', 'csv', 'jpg', 'jpeg', 'png'];
const error = ref('');

function isValidFile(file:File) {
  if(!file)
  return false;
  const mimeValid = allowedFileTypes.includes(file.type);
  const extension = file.name ? file.name.split('.').pop()?.toLowerCase() ?? '' : '';
  const extensionValid = allowedExtensions.includes(extension);

  return mimeValid || extensionValid;
}
const requestIdNum = computed(() => Number(props.requestId));

const onFileChange = async (file: File | undefined) => {
  selectedFile.value = file ? file : null;
  if (!selectedFile.value) return;
  const error = validate();
  if (error) {
    alert(error);
    selectedFile.value = null;
    return;
  }
  await upload();
};

const validate = (): string | null => {
  error.value=""
  if (!selectedFile.value) return t('Please select a file');
  const maxSize = 10 * 1024 * 1024; // 10 MB

    if (!isValidFile(selectedFile.value)) {

    selectedFile.value = null;
    error.value = 'Invalid file type. Please select a supported format.';
    return null;
  }
  if (selectedFile.value && selectedFile.value.size > maxSize) return t('File must be less than 10MB');
  return null;
};

const upload = async () => {
  const error = validate();
  if (error) {
    alert(error);
    return;
  }
  if (!selectedFile.value) return;
  isUploading.value = true;
  try {
    await uploadRequestFile(requestIdNum.value, selectedFile.value);
    selectedFile.value = null;
    emit('uploaded');
  } catch (e) {
    console.error('Upload failed', e);
    alert(t('Upload failed'));
  } finally {
    isUploading.value = false;
  }
};
</script>

<template>
  <div>
    <v-file-input
      :model-value="null"
      :show-size="true"
      :counter="false"
      :multiple="false"
      label="Select file"
      density="compact"
      accept=""
      prepend-icon="attach_file"
      :disabled="isUploading"
      @update:model-value="onFileChange($event as any)"
    />
  </div>
  <div v-if="error">{{error}}</div>
</template>

<style scoped>
</style>

