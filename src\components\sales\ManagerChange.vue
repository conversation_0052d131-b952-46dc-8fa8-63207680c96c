<template>
    <v-dialog v-model="dialogModel" max-width="500px">
      <v-card>
        <v-card-title>{{ t('page.sales_request_form.customer_details.searchHeader') }}</v-card-title>
<v-card-text>
  <AutoCompleteManagerSEarch
    v-model="selectedSalesManager"
    :label="t('page.sales_request_form.customer_details.managerSearchLabel')"
    :employees="salesManaers"
    :loading="loadingSalesManager"
    :error-messages="errorMessage"
    :search="salesManagerSearch"
    :disabled="isManagerFiledDisabled"
    @update:search="onSalesManagerSearch"
  />

  <v-slide-y-transition>
    <div
      v-if="isManagerFiledDisabled"
      class="d-flex align-center pa-3 mt-2 rounded-lg"
      style="background: #e8f5e9; border: 1px solid #4caf50;"
    >
      <v-icon color="green" class="mr-2">check</v-icon>
      <span class="text-body-2" style="color: #2e7d32; font-weight: 500;">
        Manager has already approved
      </span>
    </div>
  </v-slide-y-transition>
</v-card-text>


  <v-card-text>
    <AutoCompleteManagerSEarch
      v-model="selectedRegionalLeader"
      :label="t('page.sales_request_form.customer_details.managerSearchLabelRl')"
      :employees="regionalLeaders"
      :loading="loadingRegionalLeader"
      :error-messages="errorMessageRl"
      :disabled="isRegionalLeaderFiledDisabled"
      :search="regionalLeaderSearch"
      @update:search="onRegionalLeaderSearch"
    />
  </v-card-text>
        <v-card-actions class="justify-end">
          <v-btn @click="confirmSelection" color="primary" :disabled="!selectedSalesManager || !selectedRegionalLeader">{{ t('done') }}</v-btn>
          <v-btn @click="closeDialog" color="error">{{ t('cancel') }}</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </template>
  
  <script setup lang="ts">
import { CanonAuth } from '@/lib/canonAuth';
import { CanonAuthState, Channel_Action, type Employee } from '@/lib/common/types';
  import { authFail } from '@/composables/auth/setAuthState';

import { getSearchedUserReportes, getUserReportes } from '@/lib/msGraph';
import { useAppStore } from '@/stores/AppStore';
import { useUserStore } from '@/stores/UserStore';
import { debounce } from 'lodash';
import { ref, computed, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { getSalesRequest, saveSalesRequest, updateSalesApprovers } from '@/services/salesRequestService';
import AutoCompleteManagerSEarch from './AutoCompleteManagerSEarch.vue';
import { getuserListByRole } from '@/lib/api';
import { DsdRequestStatus } from '@/enums/DsdRequestStatus';
import { useSnackbarStore } from '@/stores/SnackbarStore';
  
  const { t } = useI18n();
  const appStore = useAppStore();

// Props
const props = defineProps({
  modelValue: Boolean,
  itemId: Number,
});
const snackbarStore = useSnackbarStore();

  const salesManaers = ref<Employee[]>([]);
  const regionalLeaders = ref<Employee[]>([]);
    const errorMessage = ref('');
    const errorMessageRl = ref('');
    const isSelectedUserManager = ref(false);
    const isManagerFiledDisabled = ref(false);
    const isRegionalLeaderFiledDisabled = ref(false);
    const requestStatus = ref(0);

  
const loadingSalesManager = ref(false)
const loadingRegionalLeader = ref(false)
const salesManagerSearch = ref('')
const regionalLeaderSearch = ref('')
  const emit = defineEmits(['update:modelValue', 'confirm']);
  
  const selectedManager = ref<Employee | null>(null);

  const dialogModel = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
  });
  onMounted(() => {
    salesManaers.value=[]
    regionalLeaders.value=[]
});
const selectedSalesManager = ref<Employee | null>(null);
const selectedRegionalLeader = ref<Employee | null>(null);
// Fetch sales request details
const fetchSalesRequestDetails = async () => {
  if (!props.itemId) return;
  try {
    const response = await getSalesRequest(props.itemId);
    requestStatus.value=response.data.dsdRequestStatus;
    isRegionalLeaderFiledDisabled.value = response.data.dsdRequestStatus == DsdRequestStatus.RL_APPROVAL_PENDING ? false : true;
    isManagerFiledDisabled.value = response.data.dsdRequestStatus == DsdRequestStatus.MANAGER_APPROVAL_PENDING ? false : true;
    selectedSalesManager.value = {
      id: response.data.salesManagerOid,
      displayName: response.data.salesManagerName,
   mail:'',
      mailNickname:  '',
      jobTitle: '',
      mobilePhone: '',
      department:'',
      employeeId: '',
      office:  '',
    };
    selectedRegionalLeader.value = {
      id: response.data.regionalLeaderOid,
      displayName: response.data.regionalLeaderName,
      mail:'',
      mailNickname:  '',
      jobTitle: '',
      mobilePhone: '',
      department:'',
      employeeId: '',
      office:  '',
    };
  } catch (error) {
    console.error('Failed to fetch sales request details:', error);
  }
};

// Watch for dialog opening
watch(() => dialogModel.value, (newVal) => {
  if (newVal) fetchSalesRequestDetails();
});


  const confirmSelection = () => {
   handleManagerChange( {regionalLeaderOid:selectedRegionalLeader.value?.id,salesManagerOid:selectedSalesManager.value?.id});
  };
  const handleManagerChange = async (data:any) => {
    try {
        appStore.startLoader('loadingRequests','Updating Sales Request...');
            const response = await updateSalesApprovers((props.itemId !== undefined ? props.itemId.toString() : ''), data);
          snackbarStore.show({
        text: 'Sales Request updated successfully',
        color: 'info',
        icon: 'info',
        timeout: 3000
      });
          dialogModel.value = false;

        appStore.stopLoader('loadingRequests');
    } catch (error) {
        console.error('Failed to update sales request managers:', error);
             snackbarStore.show({
        text: 'Failed to update Sales Request',
         color: 'error',
        icon: 'alert',
        timeout: 3000
      });
    } finally {
        appStore.stopLoader('loadingRequests');
    }
};
  const closeDialog = () => {
    emit('update:modelValue', false);
  };
  const checkIfManager =async (type:string='salesManager')=>{
    if(type=='salesManager'){
        selectedManager.value=selectedSalesManager.value
    }else{
        selectedManager.value=selectedRegionalLeader.value
    }
    if(!selectedManager.value){
        return
    }
    errorMessage.value = '';
    const userStore= useUserStore()
       
    try{
      appStore.startLoader( 'CheckingManager', t( 'Checking if user is manager' ) );
      
      const tokenResult = await CanonAuth.getToken( undefined, true );
        // Validate if valid ID and Access Token are available.
        if ( !tokenResult || !tokenResult.idToken || !tokenResult.accessToken)
        {
            return authFail( CanonAuthState.SESSION_EXPIRED );
        }
   
     isSelectedUserManager.value  = await getSearchedUserReportes( tokenResult.accessToken,selectedManager.value.id);
    if(!isSelectedUserManager.value){
        if(type=='salesManager'){
          errorMessage.value = t('page.sales_request_form.customer_details.notaManagerError');
        }else{
          errorMessageRl.value = t('page.sales_request_form.customer_details.notaManagerError');
        }
    }
    }

    finally{
      appStore.stopLoader( 'CheckingManager');

    }
 
  }
  // Fetch employees based on search query
const fetchEmployees = async (searchQuery: string): Promise<Employee[]> => {

  if (!searchQuery) {
    isSelectedUserManager.value = false;
    regionalLeaders.value = [];
    return []; // Preventing API call on selection and blank string
  }
  if (selectedManager.value?.displayName == searchQuery) {
    return []; // Preventing API call on selection and blank string
  }

  try {
    const response = await getuserListByRole(Channel_Action.SALES_MANAGER,searchQuery)
    return response.data.map((user: any) => ({
      id: user.accountId,
      displayName: user.displayName,
      mail: user.email,

    }));
  } catch (error) {
    console.error('Error fetching employees:', error);
    return [];
  } finally {
    loadingRegionalLeader.value = false
    loadingSalesManager.value = false
  }
};


const onSalesManagerSearch = (val: string) => {
  salesManagerSearch.value = val
  debouncedFetchSalesManagers(val)
}
const debouncedFetchSalesManagers = debounce(async (val: string) => {
  loadingSalesManager.value = true
  try {
    // If val is empty, do not clear the list; keep the last options
    if (val) salesManaers.value = await fetchEmployees(val)
  } finally {
    loadingSalesManager.value = false
  }
}, 300)

const onRegionalLeaderSearch = (val: string) => {
  regionalLeaderSearch.value = val
  debouncedFetchRegionalLeaders(val)
}
const debouncedFetchRegionalLeaders = debounce(async (val: string) => {
  loadingRegionalLeader.value = true
  try {
    if (val) regionalLeaders.value = await fetchEmployees(val)
  } finally {
    loadingRegionalLeader.value = false
  }
}, 300)

  </script>
  