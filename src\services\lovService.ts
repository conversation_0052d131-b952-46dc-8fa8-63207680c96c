import axios from 'axios';
import { useAppStore } from '@/stores/AppStore';
import getAccessToken from '@/composables/auth/getAccessToken.js';

// Dedicated axios client for List-Of-Values (LOV) endpoints
const lovClient = axios.create({
  baseURL: import.meta.env.VITE_APP_API_BASE_URL,
  headers: {
    'Content-Type': 'application/json'
  }
});

// Attach app-store bearer token automatically
lovClient.interceptors.request.use(
  (config) => {
    const appStore = useAppStore();
    const token = appStore.authenticationToken;
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => Promise.reject(error)
);

export interface LovItem {
  code: string;
  description: string;
  lookupCode:string,
  value:string,
  displayOrder?: number
}

/**
 * Fetch drop-down list values for a given category.
 * Endpoint: GET /dsd/lov/{category}
 */
// Simple session-storage cache key builder
const buildLovCacheKey = (category: string) => `lov_${category}`;

/**
 * Fetch drop-down list values for a given category.
 * ‑ Checks sessionStorage for a cached result first. 
 * ‑ Pass `forceRefresh = true` to skip cache and refresh.
 * Endpoint: GET /dsd/lov/{category}
 */
export const getLov = async (category: string, forceRefresh = false): Promise<LovItem[]> => {
  const cacheKey = buildLovCacheKey(category);

  if (!forceRefresh) {
    const cached = sessionStorage.getItem(cacheKey);
    if (cached) {
      try {
        return JSON.parse(cached) as LovItem[];
      } catch {
        // Corrupted cache; clear it and continue to fetch fresh data
        sessionStorage.removeItem(cacheKey);
      }
    }
  }

  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { Authorization: `Bearer ${token}` };
  const { data } = await lovClient.get<LovItem[]>(`/dsd/lov/${category}`, {
    headers: {
      ...lovClient.defaults.headers.common,
      ...additionalHeaders
    }
  });

  // Cache freshly fetched data for future use in this browser session
  try {
    sessionStorage.setItem(cacheKey, JSON.stringify(data));
  } catch {
    // Failing to write to sessionStorage is non-critical; ignore silently
  }

  return data;
};

// Exported enum-like object for reference in components
export const LovCategories = {
  SALES_CHANNEL: 'SALES_CHANNEL',
  PRICING_CATEGORY_CODE: 'PRICING_CATEGORY_CODE',
  IMPLEMENTATION: 'IMPLEMENTATION',
  PAYMENT_MODE: 'PAYMENT_MODE',
  SUPPLIER_TYPE: 'SUPPLIER_TYPE',
  LEASE_TERM: 'LEASE_TERM',
  CONTRACT_TYPE: 'CONTRACT_TYPE',
  BILLING_PERIOD: 'BILLING_PERIOD',
  BRANCH: 'BRANCH',
  POTENTIAL_COMPETITION: 'POTENTIAL_COMPETITION',
  CURRENT_INCUMBENT: 'CURRENT_INCUMBENT',
  PORTFOLIO: 'PORTFOLIO',
  SERVICE_VALUE_PACK: 'SERVICE_VALUE_PACK',
  MODEL_CATEGORY: 'MODEL_CATEGORY',
  CATEGORY_VALUE_PACK: 'CATEGORY_VALUE_PACK'
} as const;
