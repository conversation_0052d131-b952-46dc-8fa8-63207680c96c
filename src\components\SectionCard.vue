// SectionCard.vue
<template>
  <v-card class="my-4" :elevation="2" variant="outlined">
    <v-card-title class="bg-blue-grey-lighten-4 py-2">
      <span class="text-subtitle-1 font-weight-medium">{{ title }}</span>
    </v-card-title>
    <v-divider></v-divider>
    <v-card-text>
      <slot></slot> <!-- Content goes here -->
    </v-card-text>
  </v-card>
</template>

<script setup>
import { defineProps } from 'vue';

defineProps({
  title: {
    type: String,
    required: true
  }
});
</script>

<style scoped>
/* Add specific styles for the section card if needed */
</style>
