<script setup lang="ts">
/**
 * @file Location Manager component for managing customer locations.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, reactive, computed, watch } from 'vue';
import { addLocation as addLocationApi, AddLocationPayload, Location as ApiLocation } from '@/services/salesRequestService';
import { useI18n } from 'vue-i18n';

// Add language support
const { t } = useI18n();

// Define the Location interface
interface Location {
    locationId: number;
    customerId: number;
    displayName: string | null;
    locationType: string;
    isPrimary: 'Y' | 'N';
    addressLine1: string;
    addressLine2: string | null;
    addressLine3: string | null;
    city: string;
    state: string;
    country: string;
    postalCode: string;
}

// Props
import { PropType } from 'vue';

const props = defineProps({
    modelValue: Object,
    error: String,
    locations: Array,
    customerId: Number,
    showAddButton: {
        type: Boolean,
        default: true
    }
});

// Internal reactive copy of locations to allow local mutation without mutating props directly
const internalLocations = ref<Location[]>([]);

watch(
    () => props.locations,
    (newVal: Location[] | null) => {
        internalLocations.value = newVal ? [...newVal] : [];
    },
    { immediate: true }
);

// Use internalLocations for the v-select items
const locationItems = computed<Location[]>(() => internalLocations.value);

// Computed property to get selected location by ID
const selectedLocationId = computed({
    get: () => props.modelValue?.locationId || null,
    set: (value: number | null) => {
        const location = value ? props.locations?.find(loc => loc.locationId === value) : null;
        emit('update:modelValue', location);
    }
});

// Emits
const emit = defineEmits(['update:modelValue', 'validate', 'location-added']);



// Dialog state
const showDialog = ref(false);

// New location form
const newLocation = reactive({
    displayName: '',
    addressLine1: '',
    addressLine2: '',
    addressLine3: '',
    city: '',
    state: '',
    province: '', // Add province field
    postalCode: '',
    country: 'CA', // Default to Canada
    phone: '' // Add phone field
});

// Form validation state
const formErrors = reactive({
    name: '',
    addressLine1: '',
    city: '',
    province: '',
    postalCode: '',
    phone: ''
});

// Province options
const provinceOptions = ['Alberta', 'British Columbia', 'Manitoba', 'Nova Scotia', 'Ontario', 'Quebec'];

// Validate the form
const validateForm = () => {
    let isValid = true;

    // Reset errors
    Object.keys(formErrors).forEach(key => {
        formErrors[key as keyof typeof formErrors] = '';
    });
    
    // Validate required fields
    // if (!newLocation.displayName.trim()) {
    //     formErrors.name = 'Location name is required';
    //     isValid = false;
    // }
    
    if (!newLocation.addressLine1.trim()) {
        formErrors.addressLine1 = 'Address line 1 is required';
        isValid = false;
    }
    
    if (!newLocation.city.trim()) {
        formErrors.city = 'City is required';
        isValid = false;
    }
    
    if (!newLocation.province) {
        formErrors.province = 'Province is required';
        isValid = false;
    }
    
    if (!newLocation.postalCode.trim()) {
        formErrors.postalCode = 'Postal code is required';
        isValid = false;
    } else {
        // Canadian postal code regex
        const regex = /^[ABCEGHJKLMNPRSTVXY][0-9][ABCEGHJKLMNPRSTVWXYZ] ?[0-9][ABCEGHJKLMNPRSTVWXYZ][0-9]$/i;
        if (!regex.test(newLocation.postalCode)) {
            formErrors.postalCode = 'Invalid postal code format';
            isValid = false;
        }
    }
    
    // if (!newLocation.phone.trim()) {
    //     formErrors.phone = 'Phone number is required';
    //     isValid = false;
    // } else {
    //     // Basic phone validation
    //     if (!/^\d{10,}$/.test(newLocation.phone.replace(/\D/g, ''))) {
    //         formErrors.phone = 'Invalid phone number';
    //         isValid = false;
    //     }
    // }
    
    return isValid;
};

// Helper to reset the new-location form fields
const resetNewLocationForm = () => {
    Object.keys(newLocation).forEach(key => {
        newLocation[key as keyof typeof newLocation] = key === 'country' ? 'CA' : '';
    });
};

// Add a new location via API
const addLocation = async () => {
    if (!validateForm()) return;

    const payload: AddLocationPayload = {
        customerId: props.customerId,
        displayName: newLocation.displayName || 'Location',
        locationType: 'HQ',
        isPrimary: (props.locations?.length ?? 0) === 0 ? 'Y' : 'N',
        addressLine1: newLocation.addressLine1,
        addressLine2: newLocation.addressLine2 || null,
        addressLine3: newLocation.addressLine3 || null,
        city: newLocation.city,
        state: newLocation.province,
        country: newLocation.country,
        postalCode: newLocation.postalCode
    };

    let createdLoc: ApiLocation;
    try {
        createdLoc = await addLocationApi(payload);
    } catch (e) {
        console.error('Add location failed', e);
        formErrors.addressLine1 = 'Failed to add location';
        return; // stop further processing on API error
    }

    // --- Success flow ---
    internalLocations.value.push(createdLoc);
    emit('location-added', createdLoc);
    emit('update:modelValue', createdLoc);

    showDialog.value = false;
    resetNewLocationForm();
};



// Custom display function for the v-select
const customItemText = (item: Location) => {
  const addressParts = [
    item.addressLine1,
    item.city,
    item.state,
    item.postalCode
  ].filter(part => part && part.trim() !== '');
  
  return addressParts.join(', ');
};
</script>

<template>
    <div class="d-flex align-center">
        <v-select
            density="compact"
            :items="locationItems"
            :item-title="customItemText"
            item-value="locationId"
            return-object
            :model-value="props.modelValue"
            :error-messages="props.error"
            @update:model-value="$emit('update:modelValue', $event)"
            @blur="$emit('validate')"
            :label="t('page.sales_request_form.customer_details.location')"
            :no-data-text="t('page.sales_request_form.worksheets.messages.no_locations_found')"
            variant="outlined"
            clearable
        >
            <template v-slot:selection="{ item }">
                <div class="text-truncate">
                    {{ customItemText(item.raw) }}
                </div>
            </template>
            <template v-slot:item="{ item, props }">
                <v-list-item v-bind="props">
                    <!-- <v-list-item-title class="text-truncate">{{ item.raw.displayName }}</v-list-item-title> -->
                    <v-list-item-subtitle class="text-truncate">{{ customItemText(item.raw) }}</v-list-item-subtitle>
                </v-list-item>
            </template>
        </v-select>
        
        <v-btn
            v-if="props.showAddButton !== false"
            icon
            variant="text"
            color="primary"
            class="ml-2 align-self-center"
            style="margin-top: -20px;"
            :disabled="!props.customerId"
            @click="showDialog = true"
        >
            <v-icon>add</v-icon>
        </v-btn>
        
        <!-- New Location Dialog -->
        <v-dialog v-model="showDialog" max-width="600px" persistent>
            <v-card>
                <v-card-title>
                    <span class="text-h5">Add New Location</span>
                </v-card-title>
                
                <v-card-text>
                    <v-container>
                        <v-row>
                            <!-- <v-col cols="12">
                                <v-text-field
                                    v-model="newLocation.name"
                                    :error-messages="formErrors.name"
                                    label="Location Name *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col> -->
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newLocation.addressLine1"
                                    :error-messages="formErrors.addressLine1"
                                    label="Address Line 1 *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newLocation.addressLine2"
                                    label="Address Line 2"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12">
                                <v-text-field
                                    v-model="newLocation.addressLine3"
                                    label="Address Line 3"
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="newLocation.city"
                                    :error-messages="formErrors.city"
                                    label="City *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <v-col cols="12" md="6">
                                <v-select
                                    v-model="newLocation.province"
                                    :items="provinceOptions"
                                    :error-messages="formErrors.province"
                                    label="Province *"
                                    required
                                    density="compact"
                                ></v-select>
                            </v-col>
                            
                            <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="newLocation.postalCode"
                                    :error-messages="formErrors.postalCode"
                                    label="Postal Code *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col>
                            
                            <!-- <v-col cols="12" md="6">
                                <v-text-field
                                    v-model="newLocation.phone"
                                    :error-messages="formErrors.phone"
                                    label="Phone *"
                                    required
                                    density="compact"
                                ></v-text-field>
                            </v-col> -->
                        </v-row>
                    </v-container>
                    <small>* indicates required field</small>
                </v-card-text>
                
                <v-card-actions>
                    <v-spacer></v-spacer>
                    <v-btn color="blue-darken-1" variant="text" @click="showDialog = false">
                        Cancel
                    </v-btn>
                    <v-btn color="blue-darken-1" variant="elevated" @click="addLocation">
                        Save
                    </v-btn>
                    <!-- <v-btn color="blue-darken-1" variant="outlined" @click="addLocation">
                        Save and Select
                    </v-btn> -->
                </v-card-actions>
            </v-card>
        </v-dialog>
    </div>
</template>

<style scoped>
.text-truncate {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}
</style>


