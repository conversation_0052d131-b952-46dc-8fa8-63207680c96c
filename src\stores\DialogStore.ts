import { defineStore } from 'pinia';

export const useDialogStore = defineStore('dialog', {
  state: () => ({
    isLocationDialogOpen: false,
    isEmailDialogOpen: false,
    isConfirmDialogOpen: false,
    isConfirmOptionsDialogOpen: false,
    confirmCallback: null as (() => void) | null,
    dialogTitle: '',
    dialogMessage: '',
    confirmButtonText: 'Yes',
    cancelButtonText: 'No',
  }),
  actions: {
    openConfirmlDialog() {
      this.isConfirmDialogOpen = true;
    },
    closeConfirmDialog() {
      this.isConfirmDialogOpen = false;
    },
    openOptionsConfirmlDialog() {
      this.isConfirmOptionsDialogOpen = true;
    },
    closeOptionsConfirmDialog() {
      this.isConfirmOptionsDialogOpen = false;
    },
    setConfirmCallback(callback: () => void) {
      this.confirmCallback = callback;
    },
    executeConfirmCallback() {
      if (this.confirmCallback) {
        this.confirmCallback();
        this.confirmCallback = null;
      }
    },
    setDialogTitle(title: string) {
      this.dialogTitle = title;
    },
    setDialogMessage(message: string) {
      this.dialogMessage = message;
    },
    setConfirmButtonText(text: string) {
      this.confirmButtonText = text;
    },
    setCancelButtonText(text: string) {
      this.cancelButtonText = text;
    },
  },
});
