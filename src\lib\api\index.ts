/**
 * @file Includes all API calls to backend.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { AxiosResponse, AxiosInstance } from 'axios';
import getAccessToken from '@/composables/auth/getAccessToken.js';

import type {
    PostLoginRequest,
    PostLoginResponse,
    UpdateUserProfileRequest
} from './types.ts';

import axios from 'axios';
import { useAppStore } from '@/stores/AppStore.js';


// Augment AxiosRequestConfig with loader controls
declare module 'axios' {
  export interface AxiosRequestConfig {
    showLoader?: boolean;       // default: true
    loaderMessage?: string;     // optional custom loader message
    loaderId?: string;          // internal use: set when loader is started
  }
}

/**
 * ----
 * Main
 * ----
 */

// Create an Axios instance with the base URL and default headers.
const api : AxiosInstance = axios.create
({
    baseURL : import.meta.env.VITE_APP_API_BASE_URL,
    headers :
    {
        'Content-Type': 'application/json',
    }
});

// Add a request interceptor to include the auth token from the app store
// Request interceptor – attaches auth token **and** starts global loader
api.interceptors.request.use(
  (config) => {
    const appStore = useAppStore();

    // ----- Authentication token -----
    const token = (appStore as any).authenticationToken;
    if (token) {
      // Ensure header object exists first
      if (!config.headers) config.headers = {} as any;
      (config.headers as any).Authorization = `Bearer ${token}`;
    }

    // ----- Global loader handling -----
    // Respect per-request toggle: default true
    const showLoader = config.showLoader !== false;
    if (showLoader) {
      const loaderId = `req-${Date.now()}-${Math.random().toString(36).substring(2, 8)}`;
      const loaderMessage: string | undefined = config.loaderMessage;
      appStore.startLoader(loaderId, loaderMessage);
      config.loaderId = loaderId;
    }

    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor – stops the corresponding loader once the request is done (success or error)
api.interceptors.response.use(
  (response) => {
    const appStore = useAppStore();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const loaderId = response.config.loaderId as string | undefined;
    if (loaderId) {
      appStore.stopLoader(loaderId);
    }
    return response;
  },
  (error) => {
    const appStore = useAppStore();
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-ignore
    const loaderId = error.config?.loaderId as string | undefined;
    if (loaderId) {
      appStore.stopLoader(loaderId);
    }
    return Promise.reject(error);
  }
);
/**
 * -------
 * Exports
 * -------
 */

/**
 * Sends request that indicates a user has logged in.
 *
 * @param {PostLoginRequest} loginData
 * @return {*}  {Promise <AxiosResponse <PostLoginResponse> >}
 */
// export const postLogin = ( loginData: PostLoginRequest ): Promise <AxiosResponse <PostLoginResponse>> =>
// {
//     return api.post<PostLoginResponse>( '/login', loginData );
// };

/**
 * Sends request to update users profile details.
 * !! TODO !! Update this to your API.
 *
 * @param {UpdateUserProfileRequest} profileData
 * @return {*}  {Promise <AxiosResponse <void> >}
 */
// export const updateUserProfile = ( microsoftAccountId : string, profileData: UpdateUserProfileRequest ): Promise <AxiosResponse <void>> =>
// {
//     return api.patch<void>( `/users/${ microsoftAccountId }`, profileData );
// };

/**
 * Sends request to get agreement event log.
 *
 * @param {string} deviceAgreementId
 * @return {*}  {Promise <AxiosResponse <void> >}
 */
export const agreementEventLog = async (deviceAgreementId: string): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);

    try {
      const response = await api.get<any>(`/deviceAgreement/byId/${deviceAgreementId}/eventLog`, {
        headers: {
          Authorization: `Bearer ${token}`
        }
      });

      return response;
    } catch (error) {
      console.error('Error fetching WiseTrack asset by barcode:', error);
      throw error;
    }
  };

  export  const  checkUserDetails = async (payload:any): Promise <AxiosResponse <any>> =>
    {
        const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.post<any>( `/user`, payload,  {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });
    };

      export  const  getuserListByRole = async (role:string,query:string): Promise <AxiosResponse <any>> =>
    {
        const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.get<any>( `/user/byRole/${role}?query=${query}`,  {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });
    };

    export const adminAddUserUpdateUser = async (userInput: any): Promise<AxiosResponse<any>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.post<any>(
            '/user/admin/addUser',
            userInput,
            {
                headers: {
                    ...api.defaults.headers.common,
                    ...additionalHeaders
                }
            }
        );
    };

  export const exportUsersAPI = async () => {
   const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
   const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
    return api.get<any>('/user/export', {
      responseType: 'blob',
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
});
  }



    export const modifyUser = async (accountId: string, userUpdate: any): Promise<AxiosResponse<any>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.patch<any>(
            `/user/admin/${accountId}`,
            userUpdate,
            {
                headers: {
                    ...api.defaults.headers.common,
                    ...additionalHeaders
                }
            }
        );
    };

    export  const  updateUserProfile = async ( microsoftAccountId : string, profileData: UpdateUserProfileRequest ): Promise <AxiosResponse <PostLoginResponse>> =>
      {
          const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
          const additionalHeaders = {
              'Authorization': `Bearer ${token}`
          };
          return api.patch<PostLoginResponse>( `/user/${ microsoftAccountId }`, profileData,   {
              headers: {
                  ...api.defaults.headers.common,
                  ...additionalHeaders
              }
          });
      };

    export const getWorksheetData = async (): Promise<AxiosResponse<any>> => {
        const cacheKey = 'worksheetData';
        const cachedData = sessionStorage.getItem(cacheKey);

        if (cachedData) {
            const data = JSON.parse(cachedData);
            return Promise.resolve({
                data,
                status: 200,
                statusText: 'OK (from cache)',
                headers: {},
                config: {} as any,
            });
        }

        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        const response = await api.get<any>(`/item/mainframe/mainUnit/`, {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });

        if (response.data) {
            sessionStorage.setItem(cacheKey, JSON.stringify(response.data));
        }

        return response;
    };

        export const getVPRateEnabledUnits = async (): Promise<AxiosResponse<any>> => {
        const cacheKey = 'vpRateEnabledUnits';
        const cachedData = sessionStorage.getItem(cacheKey);

        if (cachedData) {
            const data = JSON.parse(cachedData);
            return Promise.resolve({
                data,
                status: 200,
                statusText: 'OK (from cache)',
                headers: {},
                config: {} as any,
            });
        }

        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        const response = await api.get<any>(`/item/mainframe/mainUnit/vpRateEnable`, {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });

        if (response.data) {
            sessionStorage.setItem(cacheKey, JSON.stringify(response.data));
        }

        return response;
    };

    export const getWorksheetSubProducts = async (mainUnitId: string): Promise<AxiosResponse<any>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        return api.get<any>(`/item/mainframe/mainUnit/${mainUnitId}`, {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });
    };

    export const getSoftwareWorksheetData = async (): Promise<AxiosResponse<any>> => {
        const cacheKey = 'softwareWorksheetData';
        const cachedData = sessionStorage.getItem(cacheKey);

        if (cachedData) {
            const data = JSON.parse(cachedData);
            return Promise.resolve({
                data,
                status: 200,
                statusText: 'OK (from cache)',
                headers: {},
                config: {} as any,
            });
        }

        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        const response = await api.get<any>(`/item/solution/mainUnit/`, {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        });

        if (response.data) {
            sessionStorage.setItem(cacheKey, JSON.stringify(response.data));
        }

        return response;
    };

      /**
       * Fetch charges rates for a hardware item based on portfolio and service value pack.
       * @param itemId Hardware item ID
       * @param portfolio Selected portfolio code
       * @param servicePack Selected service value pack code
       */
      export const getHardwareChargesRates = async (
        itemId: number,
        servicePackId: number,
        portfolioId: number,
        isDealerAcceptedSr: string = 'N'
      ): Promise<AxiosResponse<any>> => {
        const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
        const additionalHeaders = {
          'Authorization': `Bearer ${token}`
        };
        return api.get<any>(
          `/vpRateDetail`,
          {
            params: { itemId: Number(itemId), servicePackId: Number(servicePackId), portfolioId: Number(portfolioId),isDealerAcceptedSr: isDealerAcceptedSr},
            headers: {
              ...api.defaults.headers.common,
              ...additionalHeaders
            }
          }
        );
      };

      /**
 * Save VP Rate payload
 */
export const saveVpRate = async (payload: any): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.post<any>(
    '/vpRate/save',
    payload,
    {
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
    }
  );
};

/**
 * Update VP Rate payload
 */
export const updateVpRate = async (payload: any): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    'Authorization': `Bearer ${token}`
  };
  return api.patch<any>(
    '/vpRate/update',
    payload,
    {
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
    }
  );
};

export const getSoftwareWorksheetSubProducts = async (itemId: string): Promise<AxiosResponse<any>> => {
          const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
          const additionalHeaders = {
              'Authorization': `Bearer ${token}`
          };
          return api.get<any>(`/item/solution/mainUnit/${itemId}`, {
              headers: {
                  ...api.defaults.headers.common,
                  ...additionalHeaders
              }
          });
      };

      export const postLogin = (
        loginData: PostLoginRequest,
        additionalHeaders?: Record<string, string>
    ): Promise<AxiosResponse<PostLoginResponse>> => {
        const formdata = new FormData();
        formdata.append('idToken',loginData.idToken)
        return api.post<PostLoginResponse>(
            '/user',
            formdata,
            {
                headers: {
                    ...api.defaults.headers.common,
                    ...additionalHeaders
                }
            }
        );
    };


/**
 * Get approved pricing data for a request.
 * @param requestId The ID of the request
 * @returns {Promise<AxiosResponse<any>>}
 */
export const getApprovedPricing = async (requestId: string | number): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
    return api.get<any>(`/dsd/request/approvedPricing/${requestId}`, {
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
    });
};

// Export types for ease of importing.
export type {
    PostLoginRequest,
    PostLoginResponse,
    UpdateUserProfileRequest,
} ;

/**
 * Get existing User details by accountId.
 * @param {string} accountId
 * @returns {Promise<AxiosResponse<any>>}
 */
export const getUserByAccountId = async (accountId: string): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.get<any>(
        `/user/${accountId}`,
        {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

/**
 * Get VP Rate Table data.
 * @param params Optional query parameters for filtering
 * @returns {Promise<AxiosResponse<any>>}
 */
export const getVPRates = async (params?: any): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.get<any>(
        '/vpRate',
        {
            params,
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

/**
 * Get service approval options for a request.
 * @param requestId The ID of the request
 * @returns {Promise<AxiosResponse<any>>}
 */
// Get soft cost rates for a model
export const getSoftCostRates = async (modelCode: string, supportTypeId = 1): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
    Authorization: `Bearer ${token}`,
  };
  return api.get<any>(`/softCost/${supportTypeId}/${modelCode}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders,
    },
  });
};

export const getServiceApprovalOptions = async (requestId: string | number): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
    return api.get<any>(`/dsd/request/serviceApprovalOption/${requestId}`, {
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
    });
};

/**
 * Post approved service options.
 * @param payload The service approval options payload
 * @returns {Promise<AxiosResponse<any>>}
 */
export const postServiceApprovalOptions = async (payload: any[]): Promise<AxiosResponse<any>> => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = {
      'Authorization': `Bearer ${token}`
  };
    return api.post<any>(`/dsd/request/serviceApprovalOption`, payload, {
      headers: {
        ...api.defaults.headers.common,
        ...additionalHeaders
      }
    });
};

/**
 * Perform action on a request (approve/reject).
 * @param requestId The ID of the request
 * @param action The action to perform ('approve' or 'reject')
 * @returns {Promise<AxiosResponse<any>>}
 */
export const performRequestAction = async (requestId: string | number, action: 'approve' | 'reject'): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.patch<any>(
        `/dsd/request/action/${requestId}`,
        {},
        {
            params: { action },
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

/**
 * Save VP Rate data.
 * @param payload The VP Rate data to save
 * @returns {Promise<AxiosResponse<any>>}
 */
/**
 * Get details for a specific category.
 * @param categoryId The ID of the category
 * @returns {Promise<AxiosResponse<any>>}
 */
export const getCategoryDetails = async (categoryId: string | number): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.get<any>(
        `/categorydetails/${categoryId}`,
        {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

export const saveVPRate = async (payload: any): Promise<AxiosResponse<any>> => {
    const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
    const additionalHeaders = {
        'Authorization': `Bearer ${token}`
    };
    return api.post<any>(
        '/vpRate/save',
        payload,
        {
            headers: {
                ...api.defaults.headers.common,
                ...additionalHeaders
            }
        }
    );
};

/**
 * File Upload APIs
 */
export interface RequestFileItem {
  docId: number;
  name: string;
  extension?: string;
  url?: string | null;
}

// Upload a single file for a request
export const uploadRequestFile = async (requestId: number, file: File) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { 'Authorization': `Bearer ${token}` };
  const formData = new FormData();
  formData.append('file', file);
  return api.post(`/dsd/request/${requestId}/upload`, formData, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders,
      'Content-Type': 'multipart/form-data'
    }
  });
};

// List files for a request
export const listRequestFiles = async (requestId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { 'Authorization': `Bearer ${token}` };
  return api.get<RequestFileItem[]>(`/dsd/upload/${requestId}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// Delete a file by ID
export const deleteRequestFile = async (requestId: number,documentId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { 'Authorization': `Bearer ${token}` };
  return api.delete(`/dsd/request/${requestId}/remove/${documentId}`, {
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};

// Download a file (returns blob)
export const downloadRequestFile = async (requestId: number, documentId: number) => {
  const token = await getAccessToken([import.meta.env.VITE_APP_API_SCOPE], false);
  const additionalHeaders = { 'Authorization': `Bearer ${token}` };
  return api.get(`/dsd/request/${requestId}/download/${documentId}`, {
    responseType: 'blob',
    headers: {
      ...api.defaults.headers.common,
      ...additionalHeaders
    }
  });
};
