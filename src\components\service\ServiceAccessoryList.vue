<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

interface Accessory {
  id: number;
  mfpModelName: string;
  accessoryName: string;
  publishedRate: number | null;
  requestedRate: number | null;
}

const props = defineProps({
  modelValue: {
    type: Array as () => Accessory[],
    required: true,
    default: () => [],
  },
  // selectedServiceValuePack: String, // If needed for future logic
});

const emit = defineEmits(['update:modelValue']);

const localAccessories = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const createNewAccessory = (): Accessory => ({
  id: Date.now() + Math.random(), // Simple unique ID
  mfpModelName: '',
  accessoryName: '',
  publishedRate: null,
  requestedRate: null,
});

const addAccessory = () => {
  const currentAccessories = [...localAccessories.value];
  currentAccessories.push(createNewAccessory());
  localAccessories.value = currentAccessories;
};

const removeAccessory = (index: number) => {
  const currentAccessories = [...localAccessories.value];
  currentAccessories.splice(index, 1);
  localAccessories.value = currentAccessories;
};

</script>

<template>
  <v-card class="mb-4">
    <v-card-title class="d-flex justify-space-between align-center">
      Accessory Included in Deal
      <v-btn color="primary" @click="addAccessory" prepend-icon="add" size="small">
        Add Accessory
      </v-btn>
    </v-card-title>
    <v-card-text>
      <p v-if="localAccessories.length === 0" class="text-center grey--text text-caption">
        No accessories added yet.
      </p>
      <div v-for="(accessory, index) in localAccessories" :key="accessory.id" class="mb-3 pa-2 border rounded">
        <v-row dense align="center">
          <v-col cols="12" md="3">
            <v-text-field
              v-model="accessory.mfpModelName"
              label="MFP Model Name"
              density="compact"
              hide-details="auto"
            />
          </v-col>
          <v-col cols="12" md="4">
            <v-text-field
              v-model="accessory.accessoryName"
              label="Accessory Included (With Separate Service Charge)"
              density="compact"
              hide-details="auto"
            />
          </v-col>
          <v-col cols="5" md="2">
            <v-text-field
              v-model.number="accessory.publishedRate"
              label="Published Rate"
              type="number"
              prefix="$"
              density="compact"
              hide-details="auto"
            />
          </v-col>
          <v-col cols="5" md="2">
            <v-text-field
              v-model.number="accessory.requestedRate"
              label="Requested Rate"
              type="number"
              prefix="$"
              density="compact"
              hide-details="auto"
            />
          </v-col>
          <v-col cols="2" md="1" class="text-right">
            <v-btn icon="delete" variant="text" color="error" size="small" @click="removeAccessory(index)" />
          </v-col>
        </v-row>
      </div>
    </v-card-text>
  </v-card>
</template>

<style scoped>
.border {
  border: 1px solid rgba(0,0,0,0.12);
}
</style>
