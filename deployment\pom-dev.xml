<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

    <modelVersion>4.0.0</modelVersion>

    <!-- DO NOT CHANGE - REQUIRED BY ALL CCI PROJECTS -->
    <parent>
        <groupId>ca.canon</groupId>
        <artifactId>canon</artifactId>
        <version>1.0</version>
    </parent>
    <!-- DO NOT CHANGE - REQUIRED BY ALL CCI PROJECTS -->
    
    <!-- Application Details -->
    <groupId>ca.canon.template</groupId>
    <artifactId>channel-support</artifactId>
    <packaging>war</packaging>
    <version>1.0</version>
    <!-- Application Details -->

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>17</java.version>

        <!-- Adjust version according to project: https://nodejs.org/en/about/previous-releases -->
        <node.version>v20.12.0</node.version>

        <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
        <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
    </properties>

    <build>
        <plugins>
            <!-- Install NodeJS and build the application -->
            <!-- Uses third party plugin: https://github.com/eirslett/frontend-maven-plugin -->
            <plugin>
                <groupId>com.github.eirslett</groupId>
                <artifactId>frontend-maven-plugin</artifactId>
                
                <!-- Obtain latest version from: https://repo1.maven.org/maven2/com/github/eirslett/frontend-maven-plugin/ -->
                <version>1.9.1</version>

                <configuration>
                    <installDirectory>target</installDirectory>
                    <nodeVersion>${node.version}</nodeVersion>
                    <workingDirectory>${project.basedir}</workingDirectory>
                </configuration>

                <executions>
                    <!-- Install NodeJS -->
                    <execution>
                        <!-- optional: you don't really need execution ids, but it looks nice in your build log. -->
                        <id>install node and npm</id>
                        <goals>
                            <goal>install-node-and-npm</goal>
                        </goals>
                        <phase>generate-resources</phase>
                    </execution>

                    <!-- Installs project dependencies -->
                    <execution>
                        <id>npm install</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>install</arguments>
                        </configuration>
                    </execution>

                    <!-- Builds the project/appliation -->
                    <execution>
                        <id>compile and build project</id>
                        <goals>
                            <goal>npm</goal>
                        </goals>
                        <configuration>
                            <arguments>run build-dev</arguments>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            
            <!-- Copy the compiled project to the target folder -->
            <plugin>
                <artifactId>maven-resources-plugin</artifactId>
                <version>${maven-resources-plugin.version}</version>
                <executions>
                    <execution>
                        <id>Copy build directory</id>
                        <phase>generate-resources</phase>
                        <goals>
                            <goal>copy-resources</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>
                                ${project.basedir}/target/${project.artifactId}-${project.version}
                            </outputDirectory>
                            <overwrite>true</overwrite>
                            <resources>
                                <resource>
                                    <!-- This path should be your project build output path -->
                                    <directory>${project.basedir}/dist/</directory>
                                    <!-- This should include all folders and static files that are built -->
                                    <includes>
                                        <include>assets/</include>
                                        <include>index.html</include>
                                        <include>favicon.ico</include>
                                    </includes>
                                </resource>
                            </resources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- https://maven.apache.org/plugins/maven-war-plugin/usage.html -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <!-- Directory for extra files to include in the WAR. This is where you place your INF/JSP files. -->
                    <warSourceDirectory>${project.basedir}/WebContent</warSourceDirectory>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>