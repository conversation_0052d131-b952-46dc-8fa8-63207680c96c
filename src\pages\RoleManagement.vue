<script setup lang="ts">
/**
 * @file Role Management page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import DataTable from '@/components/common/DataTable.vue';
import { useAppStore } from '@/stores/AppStore';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();

// Table data
const loading = ref(false);
const headers = ref([
    { title: t('page.role_management.table.header.id'), key: 'id' },
    { title: t('page.role_management.table.header.name'), key: 'name' },
    { title: t('page.role_management.table.header.description'), key: 'description' },
    { title: t('page.role_management.table.header.users'), key: 'users' },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref([
    { id: 1, name: 'Admin', description: 'Administrator role', users: 5 },
    { id: 2, name: 'User', description: 'Standard user role', users: 25 },
    { id: 3, name: 'Manager', description: 'Manager role', users: 10 },
    { id: 4, name: 'Approver', description: 'Approver role', users: 8 },
]);

// Handle table actions
const handleAction = ({ action, item }) => {
    if (action === 'edit') {
        // Handle edit action
        console.log('Edit role:', item);
    } else if (action === 'delete') {
        // Handle delete action
        console.log('Delete role:', item);
    }
};

// Load data
onMounted(() => {
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
        <h1>{{ t('page.role_management.title') }}</h1>
        
        <v-card class="mt-4">
            <v-card-title class="d-flex justify-space-between align-center">
                <span>{{ t('page.role_management.table.title') }}</span>
                <v-btn color="primary" prepend-icon="add">
                    {{ t('page.role_management.button.add_role') }}
                </v-btn>
            </v-card-title>
            
            <v-card-text>
                <DataTable 
                    :headers="headers" 
                    :items="items" 
                    :loading="loading"
                    @action="handleAction"
                >
                    <template v-slot:item.actions="{ item }">
                        <v-icon 
                            size="small" 
                            class="me-2" 
                            @click="handleAction({ action: 'edit', item })"
                        >
                            edit
                        </v-icon>
                        <v-icon 
                            size="small" 
                            color="error" 
                            @click="handleAction({ action: 'delete', item })"
                        >
                            delete
                        </v-icon>
                    </template>
                </DataTable>
            </v-card-text>
        </v-card>
    </div>
</template>