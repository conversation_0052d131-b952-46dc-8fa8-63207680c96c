<script setup lang="ts">
	/**
	 * @file Primary view template.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { onMounted } from 'vue';
    import { useAppStore } from "@/stores/AppStore";
	import AppHeader from '@/components/core/AppHeader.vue';
	import AppFooter from '@/components/core/AppFooter.vue';

    /**
     * ----
     * Main
     * ----
     */

    // Add stores.
    const appStore = useAppStore();

    // Start view initialization process.
    onMounted ( async () =>
    {
        //////////////////////////////////////////
        // PERFORM ALL VIEW PRE-LOAD TASKS HERE //
        //////////////////////////////////////////

        // AppInit is a special loader initiated when the application is first loaded in browser.
        // Stopping this loader is the responsibility of the view (not the pages!).
        appStore.stopLoader( 'AppInit' );
    });
</script>

<template>
	<div>
		<!-- Header START -->
		<app-header />
		<!-- Header END -->

		<!-- Page START -->
		<v-main>
			<router-view />
		</v-main>
		<!-- Page END -->

		<!-- Footer START -->
		<app-footer />
		<!-- Footer END -->
	</div>
</template>

<style lang="scss"></style>