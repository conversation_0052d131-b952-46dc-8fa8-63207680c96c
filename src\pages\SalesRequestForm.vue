<script setup lang="ts">
/**
 * @file New Sales Request form page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, reactive, defineProps, onMounted, watch, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import { useRouter, useRoute } from 'vue-router';
import { saveSalesRequest, getSalesRequest, validateRequest, submitSalesRequest } from '@/services/salesRequestService';
import { performRequestAction } from '@/lib/api';

// Import tab components
import CustomerDetailsTab from '@/components/sales/CustomerDetailsTab.vue';
import PaymentDetailsTab from '@/components/sales/PaymentDetailsTab.vue';
import EquipmentsTab from '@/components/sales/EquipmentsTab.vue';
import WorksheetsTab from '@/components/sales/WorksheetsTab.vue';
import PreviewTab from '@/components/sales/PreviewTab.vue';
import ServiceFormTab from '@/components/sales/ServiceFormTab.vue';
import ServiceCmacRegistration from '@/components/sales/ServiceCmacRegistration.vue';
import ValidationErrorSummary from '@/components/sales/ValidationErrorSummary.vue';
import { useSnackbarStore } from '@/stores/SnackbarStore';
import { useUserStore } from '@/stores/UserStore';
import { ApprovalStatus } from '@/lib/common/types';

/**
 * ----
 * Props
 * ----
 */
const props = defineProps({
  id: {
    type: String,
    required: false
  },
  viewOnly: {
    type: Boolean,
    default: false
  }
});

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();
const router = useRouter();
const route = useRoute();

    const userStore  = useUserStore();

// Add language support
const { t } = useI18n();

const pageTitle = computed(() => {
  if (props.viewOnly) {
    return t('page.sales_request_form.title_3', 'View Sales Request');
  }
  return editMode.value ? t('page.sales_request_form.title_2') : t('page.sales_request_form.title');
});

// Data for view-only mode
const viewOnlyData = ref<any>(null);

// Active tab tracking
const activeTab = ref(0);
const snackbarStore = useSnackbarStore();
const requestId = ref<number | null>(null);
const serviceFormId = ref<number | null>(null);

// Edit mode: true if editing, false if creating new
const editMode = ref(false);

// References to tab components for validation
const customerDetailsTabRef = ref<InstanceType<typeof CustomerDetailsTab> | null>(null);
const paymentDetailsTabRef = ref<InstanceType<typeof PaymentDetailsTab> | null>(null);
const equipmentsTabRef = ref<InstanceType<typeof EquipmentsTab> | null>(null);
const worksheetsTabRef = ref<InstanceType<typeof WorksheetsTab> | null>(null);
const previewTabRef = ref<InstanceType<typeof PreviewTab> | null>(null);
const serviceFormTabRef = ref<InstanceType<typeof ServiceFormTab> | null>(null);

// Cross-tab summaries to feed Service Form
const msrpSummary = computed(() => {
  const summary = worksheetsTabRef.value?.getSummary?.();
  return summary ? { amount: summary.totalMSRP, percentage: summary.msrpPercentage } : { amount: 0, percentage: 0 };
});
const paymentInfo = computed(() => {
  const data: any = paymentDetailsTabRef.value?.getFormData?.();
  return data || {};
});

// Preview data refs. We populate them explicitly when the user clicks the
// "Preview" button or when we load existing request data.
const previewCustomerDetails = ref<Record<string, any>>({});
const previewPaymentDetails = ref<Record<string, any>>({});
const previewEquipmentDetails = ref<Record<string, any>>({});
const previewWorksheetDetails = ref<any[]>([]);
const previewServiceRequestData = ref<Record<string, any>>({});

// CMAC Registration form data
const cmacRegistrationData = ref<any>({});

// Request status (numeric) for controlling child components
const dsdRequestStatus = ref<number | null>(null);

// Validation error state
const validationErrors = ref<Record<any, any>>({});
const showValidationErrors = ref(false);

// Confirmation dialog states
const showApproveDialog = ref(false);
const showRejectDialog = ref(false);
const actionLoading = ref(false);

// Determine if hardware items exist in worksheets

// Selected portfolioId from Customer Details tab
const selectedPortfolioId = computed<number | null>(() => {
  const data: any = customerDetailsTabRef.value?.getFormData() || {};
  return data.portfolioId ?? null;
});

// Expose customer locations and ID for child tabs
const customerLocations = computed(() => customerDetailsTabRef.value?.getLocations() || []);
const selectedCustomerId = computed(() => customerDetailsTabRef.value?.getCustomerId() || null);

const hasHardwareItemsAndPortfolio = computed(() => {
  const items = worksheetsTabRef.value?.getFormData() || [];
  const portfolioId = selectedPortfolioId.value;
  return items.some((it: any) => it.isSolution !== 'Y' && it?.isVpRateEnable == 'Y' && portfolioId); // hardware marked by isSolution !== 'Y'
});

const isServiceFormApplicable = computed(() => {
  return requestData.value?.isServiceFormApplicable;
});

// Handle form submission
const submitForm = async (validate=false) => {
  try {
    // Validate all tabs
    if(validate){
    const customerDetailsValid = await customerDetailsTabRef.value?.validateForm();
    const paymentDetailsValid = await paymentDetailsTabRef.value?.validateForm();
    // const equipmentsValid = await equipmentsTabRef.value?.validateForm();
    const worksheetsValid = await worksheetsTabRef.value?.validateForm();
    if (!customerDetailsValid || !paymentDetailsValid || !worksheetsValid) {
      snackbarStore.show({
        text: t('page.sales_request_form.validation_error'),
        color: 'error',
        timeout: 3000,
      });
      return;
    }
    }


    // Get form data from all tabs
    const customerDetails = customerDetailsTabRef.value?.getFormData();
    const paymentDetails = paymentDetailsTabRef.value?.getFormData();
    const equipmentDetails = equipmentsTabRef.value?.getFormData();
    const worksheetDetails = worksheetsTabRef.value?.getFormData();
    // Build payload matching backend's updated structure
   
    const worksheetsValid0 = await worksheetsTabRef.value?.validateForm();
      if (!worksheetsValid0) {
        activeTab.value = 3; // Move to worksheet tab
        snackbarStore.show({
          text: t('page.sales_request_form.validation_error', 'Validation Error, Quantity cannot be zero'),
          color: 'error',
          timeout: 3000,
        });
        return;
      }

      // ---------------- Service Form validation ----------------
      if (isServiceFormApplicable.value && serviceFormTabRef.value && typeof (serviceFormTabRef.value as any).validateForm === 'function') {
        const serviceFormValid0 = await (serviceFormTabRef.value as any).validateForm();
        if (!serviceFormValid0) {
          activeTab.value = 4; 
          snackbarStore.show({
            text: t('page.sales_request_form.validation_error', 'Validation Error,Service Form (color/oversize)% values required'),
            color: 'error',
            timeout: 3000,
          });
          return;
        }
      }

    const formData = {
      requestId: requestId.value ?? null,

      /* ---------------- Customer Details ---------------- */
      customerId: customerDetails?.businessId,
      locationId: customerDetails?.locationId,
      isGlobalAgreement: customerDetails?.globalAgreement,
      customerRelationship: customerDetails?.customerRelationship,
      networkScan: customerDetails?.coreItsNetworkScan,
      sfOpportunityId:customerDetails?.sfOpportunityId,
      isRfp: customerDetails?.rfp,
      isMsa: customerDetails?.msa,
      rfpDueDate: customerDetails?.rfpDueDate,
      printAssessment: customerDetails?.printAssessment,
      salesRepOid: customerDetails?.salesRepOid,
      salesManagerOid: customerDetails?.salesManagerOid,
      regionalLeaderOid: customerDetails?.regionalLeaderOid,
      salesChannel: customerDetails?.salesChannel,
      portfolioId: customerDetails?.portfolioId,
      salesBranch: customerDetails?.branch,

      /* ---------------- Payment Details ---------------- */
      pricingCategoryCode: paymentDetails?.pricingCategoryCode,
      minimumCommitmentAmount: paymentDetails?.minimumCommitment,
      foreignBuyout: paymentDetails?.foreignBuyoutTradeUp,
      leaseTermInMonth: paymentDetails?.leaseTermInMonth,
      contractType: paymentDetails?.typeOfContract,
      billingPeriod: paymentDetails?.billingPeriod,
      supplierType: paymentDetails?.supplierType,
      implementation: paymentDetails?.implementation,
      proposalDate: paymentDetails?.proposalDate,
      paymentMode: paymentDetails?.paymentMode,
      paymentJustification: paymentDetails?.paymentJustification,

      /* ---------------- Equipment / Strategy Details ---------------- */
      potentialUnitCount: equipmentDetails?.potentialUnitCount,
      installationDate: equipmentDetails?.installationDate,
      specialConsideration: equipmentDetails?.specialConsideration,
      hardwarePricingStrategy: equipmentDetails?.hardwarePricingStrategy,
      softwareSalesStrategy: equipmentDetails?.softwareSalesStrategy,
      currency: equipmentDetails?.currency || 'USD',
      currentIncumbent: equipmentDetails?.currentIncumbent,
      mfpIncumbents: equipmentDetails?.mfpIncumbents || [],
      potentialIncumbent: equipmentDetails?.potentialIncumbent || [],

     

      /* ---------------- Worksheet Details ---------------- */
      requestItems: worksheetDetails?.map((item: any) => ({
        requestItemId: item?.requestItemId ?? null,
        itemId: item.itemId,
        dealerIt: item.dealerIt ?? null,  
        dsdQuantity: item.dsdQuantity ?? null,
        msrp: item.msrp,
        requestedSellingPrice: item.requestedSellingPrice,
        approvedSellingPrice: null,
        locationId: item.location?.locationId ?? customerDetails?.locationId,
        isPrimaryProposal: item.isPrimaryProposal ?? 'N'
      }))
    };
    appStore.startLoader('Saving','Saving Sales Request...');
    
    // Submit the form data
    const response = await saveSalesRequest(formData);

    // Also submit Service Form if the tab/component exists
    if (serviceFormTabRef.value && typeof (serviceFormTabRef.value as any).submitServiceForm === 'function') {
      try {
        await (serviceFormTabRef.value as any).submitServiceForm();
      } catch (err) {
        console.error('Failed to save Service Form:', err);
      }
    }
    
    appStore.stopLoader('Saving');

    // Refresh PreviewTab data after successful save
    if (previewTabRef.value && typeof previewTabRef.value.refreshData === 'function') {
      try {
        await previewTabRef.value.refreshData();
      } catch (err) {
        console.error('Failed to refresh preview data:', err);
      }
    }

    // Show success message
    snackbarStore.show({
      text: t('page.sales_request_form.success_message'),
      color: 'success',
      timeout: 3000,
    });
    // Redirect based on edit mode
    if (response.data && response.data.requestId) {
      // After creating a new request, go to the edit page for that request
      router.push({ name: 'pageEditSalesRequest', params: { id: response.data.requestId } });
  // Refresh CustomerDetailsTab BEFORE loading data to ensure clean state
  customerDetailsTabKey.value++;
        // Wait for the component to be recreated before loading data
        await nextTick();
      if(editMode.value){
        loadRequestData(response.data.requestId);
      }
    } else {
      // After updating an existing request, go back to the list
      router.push({ name: 'allSalesRequests' });
    }
  } catch (error) {
    console.error('Error submitting form:', error);
    appStore.stopPageLoader();
    snackbarStore.show({
      text: t('page.sales_request_form.error_message'),
      color: 'error',
      timeout: 3000,
    });
  }

  }

    // Handle form submission
const validateForm = async (validate=false) => {
  try {
    // Call validation API with dummy data for now
    // const dummyValidationErrors = {
    //   "isGlobalAgreement": "Please specify global agreement",
    //   "customerRelationship": "Please enter customer relationship",
    //   "pricingCategoryCode": "Please select pricing category code",
    //   "implementation": "Please enter implementation",
    //   "softwareSalesStrategy": "Please enter software sales strategy",
    //   "mfpIncumbents[0].incumbentPercent": "Please enter incumbent percent",
    //   "serviceForm.servicePackId": "Please enter service pack",
    //   "serviceForm.servicePackDescription": "Please enter service pack description",
    //   "serviceForm.currentUsageInformation": "Please enter current usage information",
    //   "serviceForm.currentEquipmentInfo": "Please enter current equipment info",
    //   "serviceForm.serviceBusinessCase": "Please enter service business case",
    //   "serviceForm.tonerType": "Please enter toner type",
    //   "serviceForm.region": "Please enter region",
    //   "serviceForm.territory": "Please enter territory",
    //   "serviceForm.coterm": "Please enter coterm",
    //   "serviceForm.isDealerAcceptedSr": "Please enter dealer accepted sr"
    // };

    // TODO: Replace with actual API call
    // const response = await fetch(`/request/validate/${requestId.value}`);
    // const validationResult = await response.json();
    const response = await validateRequest(requestId.value);
    if(Object.keys(response.data).length == 0){
        snackbarStore.show({
                text: t('Validation successful, Proceed to submit!'),
                color: 'success',
                timeout: 3000,
            });
    }
    // Set validation errors and show them
    validationErrors.value = response.data;
    showValidationErrors.value = Object.keys(response.data).length > 0;
}
   catch (err) {
        console.error('Failed to save Service Form:', err);
      }
}

const finalsubmitForm = async () => {
  try {
 
    const response = await submitSalesRequest(requestId.value);
    // Set validation errors and show them
    if(response?.data?.errors){
    validationErrors.value = response.data.errors;
    showValidationErrors.value = Object.keys(response.data.errors).length > 0;
    }
    if(!response?.data?.errors){
    snackbarStore.show({
                text: t('Request submitted successfully!'),
                color: 'success',
                timeout: 3000,
            });
    router.push({ name: 'allSalesRequests' });
    }
}
   catch (err) {
        console.error('Failed to save Service Form:', err);
      }
}


// Load data based on the current route
const requestData = ref<any>(null);

const loadRequestData = async (id: string | undefined) => {
    editMode.value = !!id;
    if (id) {
        requestId.value = parseInt(id, 10);
        try {
            appStore.startLoader('loadingRequests','Loading Sales Request...');
            const response = await getSalesRequest(requestId.value);
            requestData.value = response.data;
            const data = requestData.value;
            // Capture request status for downstream components
            dsdRequestStatus.value = data.dsdRequestStatus ?? null;
            serviceFormId.value = data.serviceFormId ?? null;

            if (props.viewOnly) {
              viewOnlyData.value = data;
              previewCustomerDetails.value = data;
              previewPaymentDetails.value = data;
              previewEquipmentDetails.value = data;
              previewWorksheetDetails.value = data.requestItems ?? [];
            } else {
              // Wait for the next DOM update cycle to ensure all child components are mounted
              await nextTick();

              // Set form data in child tabs - ensure CustomerDetailsTab completes first
if (customerDetailsTabRef.value) {
  await (customerDetailsTabRef.value as any).setFormData(data);
  // Wait an additional tick to ensure locations are fully populated
  await nextTick();
}
              if (paymentDetailsTabRef.value) (paymentDetailsTabRef.value as any).setFormData(data);
              if (equipmentsTabRef.value) (equipmentsTabRef.value as any).setFormData(data);
              if (worksheetsTabRef.value) (worksheetsTabRef.value as any).setFormData(data);
            }

        } catch (error) {
            console.error('Error fetching request data:', error);
            snackbarStore.show({
                text: t('page.sales_request_form.error_message'),
                color: 'error',
                timeout: 3000,
            });
        } finally {
            appStore.stopLoader('loadingRequests');
            appStore.stopPageLoader();
        }
    } else {
        requestId.value = null;
            appStore.stopLoader('loadingRequests');
    }
};

// Load data initially on component mount
onMounted(() => {
    loadRequestData(props.id);
    appStore.stopPageLoader();

});

// Watch for changes in the route params and reload data
watch(() => props.id, (newId) => {
    loadRequestData(newId as string);
});

// Populate preview data automatically whenever the user navigates to the Preview tab
watch(activeTab, (newVal) => {
  // Vuetify v-tabs may emit string values ("4") even if we stored a number. Use loose equality.
  if (newVal == 5) {
    updatePreviewData();
  }
});

const goToNextTab = () => {
    if (activeTab.value < 6) {
        activeTab.value += 1;
    }
};

// Handle cancel
const cancelForm = () => {
  router.push({ name: 'allSalesRequests' });
};

// Handle preview
const previewData = async () => {
  await updatePreviewData();
  activeTab.value = 4; // Navigate to preview tab
};

// Handle navigation to field with validation error
const handleNavigateToField = (fieldKey: string) => {
  // Map field keys to tab indices
  const fieldToTabMap: Record<string, number> = {
    // Customer Details - Tab 0
    'isGlobalAgreement': 0,
    'customerRelationship': 0,
    'pricingCategoryCode': 0,
    'implementation': 0,
    'softwareSalesStrategy': 0,

    // Payment Details - Tab 1 (if needed)

    // Equipments - Tab 2
    'mfpIncumbents[0].incumbentPercent': 2,
    'mfpIncumbents[1].incumbentPercent': 2,
    'mfpIncumbents[2].incumbentPercent': 2,

    // Worksheets - Tab 3 (if needed)

    // Service Form - Tab 4 (assuming it's the last tab)
    'serviceForm.servicePackId': 4,
    'serviceForm.servicePackDescription': 4,
    'serviceForm.currentUsageInformation': 4,
    'serviceForm.currentEquipmentInfo': 4,
    'serviceForm.serviceBusinessCase': 4,
    'serviceForm.tonerType': 4,
    'serviceForm.region': 4,
    'serviceForm.territory': 4,
    'serviceForm.coterm': 4,
    'serviceForm.isDealerAcceptedSr': 4,
  };

  const tabIndex = fieldToTabMap[fieldKey];
  if (tabIndex !== undefined) {
    activeTab.value = tabIndex;

    // Pass the error to the appropriate child component
    nextTick(() => {
      const errorMessage = validationErrors.value[fieldKey];
      if (errorMessage) {
        // TODO: Pass validation errors to child components
        console.log(`Navigate to field: ${fieldKey} with error: ${errorMessage}`);
      }
    });
  }
};

// Centralised method to collect the latest data from each tab


const updatePreviewData = async () => {
  // Ensure all child components have had a chance to update their reactive state
  await nextTick();
  previewCustomerDetails.value = customerDetailsTabRef.value?.getFormData() ?? {};
  previewPaymentDetails.value = paymentDetailsTabRef.value?.getFormData() ?? {};
  previewEquipmentDetails.value = equipmentsTabRef.value?.getFormData() ?? {};
  previewWorksheetDetails.value = worksheetsTabRef.value?.getFormData() ?? [];
  previewServiceRequestData.value = serviceFormTabRef.value?.getFormData() ?? {};
};



const approveRequest = () => {
  showApproveDialog.value = true;
};

const rejectRequest = () => {
  showRejectDialog.value = true;
};

const confirmApprove = async () => {
  if (!requestId.value) {
    snackbarStore.show({
      text: 'No request ID available',
      color: 'error',
      timeout: 3000,
    });
    return;
  }

  try {
    actionLoading.value = true;
    const response = await performRequestAction(requestId.value, 'approve');
    const data = response.data;
    viewOnlyData.value = data;

    snackbarStore.show({
      text: 'Request approved successfully',
      color: 'success',
      timeout: 3000,
    });

    showApproveDialog.value = false;

    // Optionally refresh the data or navigate away
    // router.push('/requests'); // Uncomment if you want to navigate back

  } catch (error: any) {

    console.error('Error approving request:', error);
    snackbarStore.show({
      text: error.response?.data?.errorMessage || 'Failed to approve request',
      color: 'error',
      timeout: 5000,
    });
  } finally {
    actionLoading.value = false;
  }
};

const confirmReject = async () => {
  if (!requestId.value) {
    snackbarStore.show({
      text: 'No request ID available',
      color: 'error',
      timeout: 3000,
    });
    return;
  }

  try {
    actionLoading.value = true;
    await performRequestAction(requestId.value, 'reject');

    snackbarStore.show({
      text: 'Request rejected successfully',
      color: 'success',
      timeout: 3000,
    });

    showRejectDialog.value = false;

    // Optionally refresh the data or navigate away
    // router.push('/requests'); // Uncomment if you want to navigate back

  } catch (error: any) {
    console.error('Error rejecting request:', error);
    snackbarStore.show({
      text: error.response?.data?.errorMessage || 'Failed to reject request',
      color: 'error',
      timeout: 5000,
    });
  } finally {
    actionLoading.value = false;
  }
};

const goToServicePage = () => {
  if (props.id) {
    router.push({ name: 'pageServiceRequestForm', params: { id: props.id } });
  }
};
const customerDetailsTabKey = ref(0);
</script>

<template>
    <div class="pa-4">
        <div class="d-flex justify-space-between align-center mb-4">
            <h1>{{ pageTitle }}</h1>
        </div>
        
        <v-card class="mt-4">
            <!-- <v-card-title>
                {{ t('page.sales_request_form.form.title') }}
            </v-card-title> -->
            
            <v-card-text>
                <!-- Edit/New Mode -->
                <template v-if="!viewOnly">
                    <!-- Validation Error Summary -->
                    <ValidationErrorSummary
                        :errors="validationErrors"
                        :is-visible="showValidationErrors"
                        @navigate-to-field="handleNavigateToField"
                        @close="showValidationErrors = false"
                    />

                    <v-tabs class="canon-tabs"
                        v-model="activeTab" 
                        bg-color="canon"
                        color="canon"
                        slider-color="canon"
                        slider-size="16"
                        center-active
                    >
                        <v-tab value="0">{{ t('page.sales_request_form.tabs.customer_details') }}</v-tab>
                        <v-tab :disabled="!editMode" value="1">{{ t('page.sales_request_form.tabs.payment_details') }}</v-tab>
                        <v-tab :disabled="!editMode" value="2">{{ t('page.sales_request_form.tabs.equipments') }}</v-tab>
                        <v-tab :disabled="!editMode" value="3">{{ t('page.sales_request_form.tabs.worksheets') }}</v-tab>
                        <v-tab :disabled="!editMode || !isServiceFormApplicable" value="5">Service Form</v-tab>
                        <v-tab :disabled="!editMode" value="4">Preview</v-tab>
                        <v-tab :disabled="!editMode" v-if="dsdRequestStatus === 6" value="6">CMAC Registration</v-tab>

                      
                    </v-tabs>
                    
                    <v-window v-model="activeTab">
                        <v-window-item value="0" eager>
                            <CustomerDetailsTab
                                :key="customerDetailsTabKey"
                                ref="customerDetailsTabRef"
                                :validation-errors="validationErrors"
                            />
                        </v-window-item>
                        
                        <v-window-item value="1" eager>
                            <PaymentDetailsTab ref="paymentDetailsTabRef" />
                        </v-window-item>
                        
                        <v-window-item value="2" eager>
                            <EquipmentsTab
                                ref="equipmentsTabRef"
                                :validation-errors="validationErrors"
                            />
                        </v-window-item>
                          
                        <v-window-item value="3" eager>
                            <WorksheetsTab ref="worksheetsTabRef"
                                   :locations="customerLocations"
                                   :customer-id="selectedCustomerId"
                                   :customer-tab-ref="customerDetailsTabRef" />
                        </v-window-item>
                        <v-window-item value="5">
                            <ServiceFormTab 
                                ref="serviceFormTabRef"
                                :key="selectedPortfolioId"
                                :msrp-summary="msrpSummary"
                                :payment-info="paymentInfo"
                                :worksheet-items="requestData?.requestItems"
                                :portfolio-id="selectedPortfolioId"
                                :request-id="requestId"
                                :service-form-id="serviceFormId"
                                :is-active="activeTab === '5'"
                            />
                        </v-window-item>
                         <v-window-item value="6" eager>
                            <ServiceCmacRegistration
                                v-model="cmacRegistrationData"
                                :status="dsdRequestStatus"
                                :request-id="requestId"
                             />
                        </v-window-item>

                        <v-window-item value="4">
                          
                            <PreviewTab
                                ref="previewTabRef"
                                :request-id="requestId || undefined"
                            />
                        </v-window-item>

                  
                    </v-window>
                </template>

                <!-- View Only Mode -->
                <template v-else>
                    <PreviewTab
                        v-if="viewOnlyData"
                        :request-id="requestId || undefined"
                    />
                    <div v-else>
                        Loading...
                    </div>
                </template>
            </v-card-text>
            
            <v-card-actions v-if="!viewOnly" class="pa-4">
                <v-spacer></v-spacer>
                
                <v-btn 
                    color="error" 
                    variant="outlined"
                    @click="cancelForm"
                    v-if="activeTab != 4"

                >
                    {{ t('common.cancel') }}
                </v-btn>
                
                <v-btn 
                    color="info" 
                    class="ml-2"
                    v-if="activeTab < 4"

                    @click="previewData"
                >
                    Preview
                </v-btn>
                     <v-btn 
                    color="primary" 
                    class="ml-2"
                    v-if="activeTab != 4 && activeTab != 6"
                    @click="submitForm(false)"
                >
                    {{ t('Save Draft') }}
                </v-btn>
                      <v-btn
                    v-if="activeTab == 4"
                    color="success"
                    class="ml-2"
                    @click="validateForm(true)"
                >
                    Validate Form
                </v-btn>
                <v-btn 
                    v-if="activeTab == 4"
                    color="success" 
                    class="ml-2"
                    @click="finalsubmitForm()"
                >
                    {{ t('common.submit') }}
                </v-btn>
          
            </v-card-actions>
            <v-card-actions v-if="viewOnly" class="pa-4" >
                <v-spacer></v-spacer>
                <v-btn
                 v-if="(userStore.microsoftID == viewOnlyData?.salesManagerOid && viewOnlyData?.dsdRequestStatus == ApprovalStatus.STATUS_MANAGER_APPROVAL_PENDING) || (userStore.microsoftID== viewOnlyData?.regionalLeaderOid &&  viewOnlyData?.dsdRequestStatus == ApprovalStatus.STATUS_RL_APPROVAL_PENDING)"
                    color="error"
                    @click="rejectRequest"
                >
                    Reject
                </v-btn>
                <v-btn 
                v-if="(userStore.microsoftID == viewOnlyData?.salesManagerOid && viewOnlyData?.dsdRequestStatus == ApprovalStatus.STATUS_MANAGER_APPROVAL_PENDING)|| (userStore.microsoftID== viewOnlyData?.regionalLeaderOid &&  viewOnlyData?.dsdRequestStatus == ApprovalStatus.STATUS_RL_APPROVAL_PENDING)"

                    color="success"
                    class="ml-2"
                    @click="approveRequest"
                >
                    Approve
                </v-btn>
            </v-card-actions>
        </v-card>
    </div>

    <!-- Approve Confirmation Dialog -->
    <v-dialog v-model="showApproveDialog" max-width="500px" persistent>
        <v-card>
            <v-card-title class="text-h5 text-success">
                <v-icon class="mr-2">check</v-icon>
                Approve Request
            </v-card-title>
            <v-card-text>
                <p class="text-body-1 mb-4">
                    Are you sure you want to approve this sales request?
                </p>
                <p class="text-body-2 ">
                    This action cannot be undone. The request will be marked as approved.
                </p>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="grey"
                    variant="text"
                    @click="showApproveDialog = false"
                    :disabled="actionLoading"
                >
                    Cancel
                </v-btn>
                <v-btn
                    color="success"
                    variant="elevated"
                    @click="confirmApprove"
                    :loading="actionLoading"
                    :disabled="actionLoading"
                >
                    <v-icon left>check</v-icon>
                    Approve
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>

    <!-- Reject Confirmation Dialog -->
    <v-dialog v-model="showRejectDialog" max-width="500px" persistent>
        <v-card>
            <v-card-title class="text-h5 text-error">
                <v-icon class="mr-2">close</v-icon>
                Reject Request
            </v-card-title>
            <v-card-text>
                <p class="text-body-1 mb-4">
                    Are you sure you want to reject this sales request?
                </p>
                <p class="text-body-2 text-grey-darken-1">
                    This action cannot be undone. The request will be marked as rejected and the requester will be notified.
                </p>
            </v-card-text>
            <v-card-actions>
                <v-spacer></v-spacer>
                <v-btn
                    color="grey"
                    variant="text"
                    @click="showRejectDialog = false"
                    :disabled="actionLoading"
                >
                    Cancel
                </v-btn>
                <v-btn
                    color="error"
                    variant="elevated"
                    @click="confirmReject"
                    :loading="actionLoading"
                    :disabled="actionLoading"
                >
                    <v-icon left>mdi-close</v-icon>
                    Reject
                </v-btn>
            </v-card-actions>
        </v-card>
    </v-dialog>
</template>

<style scoped>
.canon-tabs .v-tab--selected {
  background-color: white !important;
  color: red;
}
.canon-tabs .v-tab--selected .v-btn__content,
.canon-tabs .v-tab--selected .v-tab__content {
  color: var(--v-theme-canon) !important;
}
</style>
