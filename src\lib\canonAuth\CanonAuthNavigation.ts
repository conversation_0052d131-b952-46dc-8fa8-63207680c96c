/**
 * @file Canon Canada authentication navigation client for Vue Router & MSAL.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { Router } from 'vue-router';
import type { NavigationOptions } from '@azure/msal-browser';

import { NavigationClient } from '@azure/msal-browser';

/**
 * -------
 * Exports
 * -------
 */

export class CanonAuthNavigation extends NavigationClient
{
    /**
     * Vue router object.
     *
     * @private
     * @type {Router}
     * @memberof CanonAuthNavigation
     */
    private router: Router;

    /**
     * Creates an instance of CanonAuthNavigation.
     * @param {Router} router
     * @memberof CanonAuthNavigation
     */
    constructor( router: Router )
    {
        super();

        this.router = router;
    }

    /**
     * Navigates to other pages within the same web application
     * You can use the useHistory hook provided by react-router-dom to take advantage of client-side routing
     * @param url 
     * @param options 
     */
    async navigateInternal ( url: string, options: NavigationOptions )
    {       
        const relativePath = url.replace( window.location.origin, '' );

        if ( options.noHistory )
        {
            this.router.replace( relativePath );
        }
        else
        {
            this.router.push( relativePath );
        }

        return false;
    }
}