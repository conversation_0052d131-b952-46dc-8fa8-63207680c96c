<script setup lang="ts">
	/**
	 * @file Primary application Vue component. Contains the root components.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { onMounted } from 'vue';
	import { useMessageStore } from "@/stores/MessageStore";
	import { useAppStore } from "@/stores/AppStore";
	import AppLoader from '@/components/core/AppLoader.vue';
	import AppMessage from '@/components/core/AppMessage.vue';
	import AppSnackbar from '@/components/core/AppSnackbar.vue';
    
    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
	const appStore = useAppStore();
	const messageStore = useMessageStore();

    // Start application initialization process.
    onMounted ( async () =>
    {
        /////////////////////////////////////////
        // PERFORM ALL APP PRE-LOAD TASKS HERE //
        /////////////////////////////////////////
        
        appStore.appInit = true;
    });
</script>

<template>
	<v-theme-provider :theme="appStore.currentTheme">
		<v-app>
			<!-- Snackbar START -->
			<app-snackbar />
			<!-- Snackbar END -->

			<!-- Message START -->
			<app-message />
			<!-- Message END -->

			<!-- Loader START -->
			<app-loader v-if="!messageStore.showMessage" />
			<!-- Loader END -->

            <!-- Router START -->
            <router-view v-if="appStore.appInit && !messageStore.disableApp"/>
            <!-- Router END -->
		</v-app>
	</v-theme-provider>
</template>

<style lang="scss">
	html
	{
		// Default application font size.
		font-size : 14px !important;
	}
</style>