/**
 * @file Gets an access token to be used with a API request. Handles token expiry by default.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { CanonAuth } from '@/lib/canonAuth';

/**
 * ----
 * Export
 * ----
*/

/**
 * Gets a access token for the given scopes.
 *
 * @param {(string[] | undefined)} [scopes=undefined] List of scopes to request.
 * @param {boolean} [forceRefresh=false] Set to true to force a refresh of the token expiry.
 * @return {*}  {(Promise <string | null>)}
 */
export default async ( scopes : string[] | undefined = undefined, forceRefresh : boolean = false ) : Promise <string | null> =>
{
    // User is authenticated.
    if ( CanonAuth.isAuthenticated() )
    {
        try
        {
            // Gets ID and Access Tokens with default (login) scopes.
            const tokenResult = await CanonAuth.getToken( scopes, forceRefresh );

            if ( tokenResult )
            {
                return tokenResult.accessToken;
            }
        }
        catch {}
    }
    
    return null;
};