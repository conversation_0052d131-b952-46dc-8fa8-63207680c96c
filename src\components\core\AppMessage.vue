<script setup lang="ts">
	/**
	 * @file Message component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import type { MessageButton } from '@/lib/common/types';

	import { ref, reactive, computed, watch } from 'vue';
	import { useMessageStore } from "@/stores/MessageStore";
	import { MessageType } from "@/lib/common/types";
    import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
	const messageStore = useMessageStore();

    // Add language support.
    const { t } = useI18n();

    let type = ref(  MessageType.INFO );
    let icon = ref( '' );
    let title = ref( '' );
    let subtitle = ref( '' );
    let body = ref( '' );
    let stack = ref( '' );
    let showContactIT = ref( false );
    let btnRefresh = ref( false );
    let btnCustom : MessageButton[] = reactive( [] );
    let btnClose = ref( false );

	// Force refresh the page.
	const refreshPage = () =>
	{
		document.location.reload();
	};

	// Close error popup.
	const closeError = () =>
	{
		messageStore.hide();
	};

	const buttonClick = async ( button : MessageButton ) =>
	{
		if ( button.close )
		{
			closeError();
		}
		if ( button.callback )
		{
			await button.callback();
		}
	}

	// Indicates the border colour based on message type.
    const borderColour = computed( () =>
    {
        if ( type.value === MessageType.INFO )
		{
			return 'box-shadow-top-info';
		}
		else if ( type.value === MessageType.ERROR )
		{
			return 'box-shadow-top-error';
		}
		else if ( type.value === MessageType.WARNING )
		{
			return 'box-shadow-top-warning';
		}
		else if ( type.value === MessageType.SUCCESS )
		{
			return 'box-shadow-top-success';
		}
		else
		{
			return 'box-shadow-top-info';
		}
    });

	// Indicates the icon colour based on message type.
	const iconColour = computed( () =>
    {
        if ( type.value === MessageType.INFO )
		{
			return 'info';
		}
		else if ( type.value === MessageType.ERROR )
		{
			return 'error';
		}
		else if ( type.value === MessageType.WARNING )
		{
			return 'warning';
		}
		else if ( type.value === MessageType.SUCCESS )
		{
			return 'success';
		}
		else
		{
			return 'info';
		}
    });

	// Indicates if the message is displaying any buttons or actions.
	const hasButtons = computed( () =>
    {
        if ( btnCustom.length > 0 || btnRefresh.value || btnClose.value )
		{
			return true;
		}

		return false;
    });
    
    // Watches for when the page is viewed in mobile resolution and disables the drawer if left open.
    watch( messageStore.messageQueue, async (new_messageQueue ) =>
    {
        if ( new_messageQueue.length > 0 )
        {
            // The timeout allows queued messages to disappear/appear naturally to the user.
            setTimeout( () =>
            {
                type.value = new_messageQueue[0].type || MessageType.INFO;
                icon.value = new_messageQueue[0].icon || '';
                title.value = new_messageQueue[0].title || '';
                subtitle.value = new_messageQueue[0].subtitle || '';
                body.value = new_messageQueue[0].body || '';
                stack.value = new_messageQueue[0].stack || '';
                showContactIT.value = new_messageQueue[0].showContactIT || false;
                btnRefresh.value = new_messageQueue[0].btnRefresh || false;
                btnCustom = new_messageQueue[0].btnCustom ? reactive( new_messageQueue[0].btnCustom ) : reactive( [] );
                btnClose.value = new_messageQueue[0].btnClose || false;

                // Set app disable status.
                messageStore.disableApp = new_messageQueue[0].disableApp || false;
                
                // Show the message.
                messageStore.showMessage = true;
            }, 100 );
        }
    });
</script>

<template>
	<v-dialog v-model="messageStore.showMessage" hide-overlay persistent>
		<v-card color="background" max-width="600" variant="elevated" class="app-message-card ma-auto">
			<v-card-item :class="borderColour" class="pb-1">
				<v-card-title :class="subtitle ? '' : 'py-2'">{{ title }}</v-card-title>
				<v-card-subtitle v-if="subtitle">{{ subtitle }}</v-card-subtitle>

				<div :style="{ position : 'absolute', right : '24px', top : '18px' }">
					<div v-if="icon">
						<v-icon :color="iconColour" :icon="icon" size="38" end />
					</div>

					<div v-else>
						<v-icon v-if="type === MessageType.SUCCESS" color="success" icon="check_circle" size="38" end />
						<v-icon v-if="type === MessageType.ERROR" color="error" icon="error" size="38" end />
						<v-icon v-if="type === MessageType.INFO" color="info" icon="info" size="38" end />
						<v-icon v-if="type === MessageType.WARNING" color="warning" icon="warning" size="38" end />
					</div>
				</div>
				
			</v-card-item>

			<v-card-text class="py-4" v-html="body"></v-card-text>

			<v-card-text v-if="stack" class="text-overline pt-0 pl-8">
				<span class="text-on-secondary bg-secondary pa-1">{{ stack }}</span>
			</v-card-text>

			<v-card-text v-if="btnRefresh" class="pt-0" v-html="t( 'component.appmessage.refresh' )"></v-card-text>
			<v-card-text v-if="showContactIT" class="pt-0">{{ t( 'component.appmessage.support' ) }}</v-card-text>
			
			<v-card-actions v-if="hasButtons" class="d-flex justify-end">
				<v-btn v-for="( button, index ) in btnCustom" :key="index" variant="elevated" :color="button.colour" @click="buttonClick( button )">{{ button.text }}</v-btn>

				<v-btn v-if="btnRefresh" variant="elevated" color="primary" @click.prevent="refreshPage">{{ t( 'component.appmessage.button.refresh.label' ) }}</v-btn>
				<v-spacer v-if="btnClose" />
				<v-btn v-if="btnClose" variant="outlined" color="primary" @click.prevent="closeError">{{ t( 'component.appmessage.button.close.label' ) }}</v-btn>
			</v-card-actions>
		</v-card>
	</v-dialog>
</template>

<style lang="scss"></style>