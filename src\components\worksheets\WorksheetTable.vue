<script setup lang="ts">
/**
 * @file Common Worksheet Table component with expandable rows.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import type { WorkSheetProduct } from '@/lib/common/types';
import { getWorksheetSubProducts, getSoftwareWorksheetSubProducts } from '@/lib/api';

/**
 * ----
 * Props
 * ----
 */
interface Props {
  data: WorkSheetProduct[];
  loading?: boolean;
  category: 'hardware' | 'software';
}

const props = withDefaults(defineProps<Props>(), {
  loading: false
});

/**
 * ----
 * Emits
 * ----
 */
const emit = defineEmits<{
  action: [{ action: string; item: WorkSheetProduct }];
}>();

/**
 * ----
 * Main
 * ----
 */
// Add language support
const { t } = useI18n();

// Table headers - showing only specified columns
const headers = ref([
  { title: t('page.worksheets_management.table.header.item_number'), key: 'itemNumber', sortable: true },
  { title: t('page.worksheets_management.table.header.display_name'), key: 'displayName', sortable: true },
  { title: t('page.worksheets_management.table.header.model_name'), key: 'modelName', sortable: true },
  { title: t('page.worksheets_management.table.header.description'), key: 'description', sortable: true },
  { title: t('page.worksheets_management.table.header.msrp'), key: 'msrp', sortable: true },
  { title: t('page.worksheets_management.table.header.wholesale_cost'), key: 'wholesaleCost', sortable: true },
  { title: t('common.actions'), key: 'actions', sortable: false }
]);

// Expanded rows tracking
const expandedRows = ref<Set<string>>(new Set());
const loadingSubProducts = ref<Set<string>>(new Set());
const subProductsCache = ref<Map<string, WorkSheetProduct[]>>(new Map());

// Extended interface for display items (includes hierarchy info)
interface DisplayItem extends WorkSheetProduct {
  _isSubProduct?: boolean;
  _parentId?: string;
  _level?: number;
  _isLoading?: boolean;
  _isEmpty?: boolean;
}

// Transform API sub-products data to display format
const transformSubProductsData = (apiData: WorkSheetProduct[]): WorkSheetProduct[] => {
  return apiData.map(item => ({
    id: item.id,
    itemId: item.itemId,
    parentItemId: item.parentItemId,
    itemNumber: item.itemNumber,
    displayName: item.displayName,
    sellByDsd: item.sellByDsd,
    sellByDealer: item.sellByDealer,
    isSolution: item.isSolution,
    isSolutionGroup: item.isSolutionGroup,
    description: item.description,
    modelName: item.modelName,
    isActive: item.isActive,
    isTerminated: item.isTerminated,
    isMainUnit: item.isMainUnit,
    isMainframe: item.isMainframe,
    itemPriceId: item.itemPriceId,
    currency: item.currency,
    msrp: item.msrp,
    wholesaleCost: item.wholesaleCost
  }));
};

// Computed property to create flattened data with sub-products inline
const flattenedData = computed(() => {
  const result: DisplayItem[] = [];

  props.data.forEach(mainItem => {
    // Add main item
    result.push({
      ...mainItem,
      _isSubProduct: false,
      _level: 0
    });

    // Add sub-products if expanded
    if (isExpanded(mainItem)) {
      if (isLoadingSubProducts(mainItem)) {
        // Add loading placeholder
        result.push({
          ...mainItem,
          id: -Math.abs(mainItem.id) - 1000, // Use negative ID to avoid conflicts
          itemNumber: '',
          displayName: 'Loading sub-products...',
          _isSubProduct: true,
          _parentId: mainItem.id.toString(),
          _level: 1,
          _isLoading: true
        });
      } else {
        const subProducts = getSubProducts(mainItem);
        if (subProducts.length === 0) {
          // Add no sub-products placeholder
          result.push({
            ...mainItem,
            id: -Math.abs(mainItem.id) - 2000, // Use negative ID to avoid conflicts
            itemNumber: '',
            displayName: 'No sub-products available',
            _isSubProduct: true,
            _parentId: mainItem.id.toString(),
            _level: 1,
            _isEmpty: true
          });
        } else {
          subProducts.forEach(subItem => {
            result.push({
              ...subItem,
              _isSubProduct: true,
              _parentId: mainItem.id.toString(),
              _level: 1
            });
          });
        }
      }
    }
  });

  return result;
});

// Toggle row expansion with API call
const toggleExpansion = async (item: WorkSheetProduct) => {
  if (!item.id) return;

  if (expandedRows.value.has(item.id.toString())) {
    // Collapse
    expandedRows.value.delete(item.id.toString());
  } else {
    // Expand - fetch sub-products if not cached
    expandedRows.value.add(item.id.toString());

    if (!subProductsCache.value.has(item.id.toString())) {
      loadingSubProducts.value.add(item.id.toString());
      try {
        // Ensure itemId is defined before making API call
        if (!item.itemId) {
          console.warn('Item ID is undefined, cannot fetch sub-products');
          expandedRows.value.delete(item.id.toString());
          return;
        }
        // Use appropriate API based on category
        const response = props.category === 'hardware'
          ? await getWorksheetSubProducts(item.itemId.toString())
          : await getSoftwareWorksheetSubProducts(item.itemId.toString());
        const subProducts = transformSubProductsData(response.data);
        subProductsCache.value.set(item.id.toString(), subProducts);
      } catch (error) {
        console.error('Error fetching sub-products:', error);
        // Remove from expanded if API call fails
        expandedRows.value.delete(item.id.toString());
      } finally {
        loadingSubProducts.value.delete(item.id.toString());
      }
    }
  }
};

// Check if row is expanded
const isExpanded = (item: WorkSheetProduct) => {
  return item.id ? expandedRows.value.has(item.id.toString()) : false;
};

// Check if sub-products are loading
const isLoadingSubProducts = (item: WorkSheetProduct) => {
  return item.id ? loadingSubProducts.value.has(item.id.toString()) : false;
};

// Get sub-products for an item
const getSubProducts = (item: WorkSheetProduct): WorkSheetProduct[] => {
  return item.id ? subProductsCache.value.get(item.id.toString()) || [] : [];
};

// Handle table actions
const handleAction = (action: string, item: WorkSheetProduct) => {
  emit('action', { action, item });
};

// Format currency
const formatCurrency = (value: number | undefined) => {
  if (value === undefined || value === null) return '$0.00';
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(value);
};

// Get MSRP value (handles both number and object types)
const getMsrpValue = (msrp: { value: number } | number | undefined): number => {
  if (msrp === undefined || msrp === null) return 0;
  if (typeof msrp === 'number') return msrp;
  return msrp.value || 0;
};



</script>

<template>
  <div>
    <v-data-table
      :headers="headers"
      :items="flattenedData"
      :loading="loading"
      item-key="id"
      class="elevation-1"
    >
      <!-- Custom row styling for sub-products -->
      <template v-slot:item="{ item, props: itemProps }">
        <tr
          :class="{
            'bg-grey-lighten-5': item._isSubProduct,
            'text-grey-darken-2': item._isSubProduct
          }"
          v-bind="itemProps"
        >
          <td v-for="header in headers" :key="header.key">
            <template v-if="header.key === 'itemNumber'">
              <div class="d-flex align-center" :style="{ paddingLeft: `${(item._level || 0) * 24}px` }">
                <!-- Show expand button only for main products -->
                <v-btn
                  v-if="!item._isSubProduct"
                  :icon="isLoadingSubProducts(item) ? 'hourglass_empty' : (isExpanded(item) ? 'expand_less' : 'expand_more')"
                  variant="text"
                  size="small"
                  :loading="isLoadingSubProducts(item)"
                  @click="toggleExpansion(item)"
                ></v-btn>
                <!-- Show sub-product indicator for sub-products -->
                <v-icon v-else-if="item._isSubProduct" size="small" class="mr-2 text-grey">
                  subdirectory_arrow_right
                </v-icon>
                <!-- Spacer for alignment when no button -->
                <div v-else style="width: 40px;"></div>
                <span class="ml-2">{{ item.itemNumber }}</span>
              </div>
            </template>
            <template v-else-if="header.key === 'displayName'">
              <div class="d-flex align-center">
                <v-progress-circular
                  v-if="item._isLoading"
                  indeterminate
                  size="16"
                  width="2"
                  class="mr-2"
                ></v-progress-circular>
                <v-icon
                  v-else-if="item._isEmpty"
                  size="16"
                  color="grey-lighten-1"
                  class="mr-2"
                >
                  inventory_2
                </v-icon>
                <span :class="{
                  'text-grey-darken-1': item._isSubProduct,
                  'font-weight-medium': !item._isSubProduct,
                  'text-grey-lighten-1': item._isEmpty,
                  'font-style-italic': item._isLoading || item._isEmpty
                }">
                  {{ item.displayName }}
                </span>
              </div>
            </template>
            <template v-else-if="header.key === 'modelName'">
              <span v-if="!item._isLoading && !item._isEmpty">{{ item.modelName || '' }}</span>
            </template>
            <template v-else-if="header.key === 'description'">
              <span v-if="!item._isLoading && !item._isEmpty">{{ item.description || '' }}</span>
            </template>
            <template v-else-if="header.key === 'msrp'">
              <span v-if="!item._isLoading && !item._isEmpty">{{ formatCurrency(getMsrpValue(item.msrp)) }}</span>
            </template>
            <template v-else-if="header.key === 'wholesaleCost'">
              <span v-if="!item._isLoading && !item._isEmpty">{{ formatCurrency(item.wholesaleCost) }}</span>
            </template>
            <template v-else-if="header.key === 'actions'">
              <!-- Only show actions for main products, not sub-products -->
              <v-menu v-if="!item._isSubProduct">
                <template v-slot:activator="{ props: menuProps }">
                  <v-btn
                    icon="more_vert"
                    variant="text"
                    size="small"
                    v-bind="menuProps"
                  ></v-btn>
                </template>
                <v-list>
                  <v-list-item
                    prepend-icon="add"
                    title="Add"
                    @click="handleAction('add', item)"
                  ></v-list-item>
                  <v-list-item
                    prepend-icon="edit"
                    title="Edit"
                    @click="handleAction('edit', item)"
                  ></v-list-item>

                  <v-list-item
                    prepend-icon="delete"
                    title="Delete"
                    @click="handleAction('delete', item)"
                  ></v-list-item>
                </v-list>
              </v-menu>
            </template>
          </td>
        </tr>
      </template>

      <!-- No Data Slot -->
      <template v-slot:no-data>
        <div class="text-center pa-4">
          <v-icon size="48" color="grey-lighten-1">{{ category === 'hardware' ? 'hardware' : 'apps' }}</v-icon>
          <div class="text-h6 mt-2">{{ t('page.worksheets_management.no_data', { category }) }}</div>
          <v-btn color="primary" class="mt-2" @click="handleAction('add', {} as WorkSheetProduct)">
            {{ t('page.worksheets_management.button.add_first', { category }) }}
          </v-btn>
        </div>
      </template>
    </v-data-table>
  </div>
</template>
