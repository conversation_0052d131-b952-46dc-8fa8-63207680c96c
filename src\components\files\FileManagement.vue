<script setup lang="ts">
import { ref, computed } from 'vue';
import FileUpload from './FileUpload.vue';
import FileList from './FileList.vue';

const props = defineProps<{ requestId: number | string; isEditMode?: boolean }>();

const listKey = ref(0);
const refreshList = () => { listKey.value++; };

const isEdit = computed(() => !!props.isEditMode);
</script>

<template>
  <div>
    <div v-if="isEdit" class="mb-4">
      <FileUpload :request-id="requestId" @uploaded="refreshList" />
    </div>
    <FileList :key="listKey" :request-id="requestId" :is-edit-mode="isEdit" @deleted="refreshList" />
  </div>
</template>

<style scoped>
</style>

