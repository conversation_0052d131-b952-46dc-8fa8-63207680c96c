<script setup lang="ts">
import { ref, computed, defineProps, defineEmits, watch } from 'vue';
import { useI18n } from 'vue-i18n';

// Define the type for a product field (matches parent)
interface ProductField {
  type: string;
  value: string | number;
  readonly?: boolean;
  min?: number;
  max?: number;
}

// Define the type for a product item (matches parent)
interface ProductItem {
  productName: ProductField;
  dsdQuantity: ProductField;
  dealerITsQuantity: ProductField;
  itemNumber: ProductField;
  requestSellingPrice: ProductField;
  msrp: ProductField;
  percentOfMsrp: ProductField;
  subProducts: ProductItem[];
}

const props = defineProps<{
  categoryTitle: string;
  products: ProductItem[];
  productOptions: Array<{ title: string; value: string }>;
  isCondensedView: boolean;
  categoryType: 'hardware' | 'software';
  productDetailsMap: Record<string, { itemNumber: string; msrp: number }>;
  getSubProductOptionsFunc: (parentValue: string) => Array<{ title: string; value: string }>;
}>();

const emit = defineEmits([
  'addProduct',
  'removeProduct',
  'addSubProduct',
  'removeSubProduct',
  'update:products' // For v-model:products pattern if needed, or direct mutation handling
]);

const { t } = useI18n();

// Internal methods to handle product updates
const updateProductDetailsInternal = (product: ProductItem, productValue: string) => {
  if (!productValue) return;
  const details = props.productDetailsMap[productValue as keyof typeof props.productDetailsMap];
  if (details) {
    product.itemNumber.value = details.itemNumber;
    product.msrp.value = details.msrp;
    updateSellingPriceInternal(product);
  }
};

const updateSellingPriceInternal = (product: ProductItem) => {
  const msrp = Number(product.msrp.value);
  let percent = Number(product.percentOfMsrp.value) || 0;

  if (percent < 1 && product.percentOfMsrp.type === 'number') product.percentOfMsrp.value = 1;
  if (percent > 100 && product.percentOfMsrp.type === 'number') product.percentOfMsrp.value = 100;
  percent = Math.max(1, Math.min(100, percent)); // Re-clamp after potential string input

  product.requestSellingPrice.value = (msrp * (percent / 100)).toFixed(2);
};

const handlePercentChangeInternal = (product: ProductItem) => {
  updateSellingPriceInternal(product);
};

const handleDsdQuantityChange = (product: ProductItem, val: string | number) => {
  product.dsdQuantity.value = Math.max(1, Number(val) || 1);
};

const handleDealerItsQuantityChange = (product: ProductItem, val: string | number) => {
  product.dealerITsQuantity.value = Math.max(0, Number(val) || 0);
};

// Calculate summary for a product and its accessories (for expanded view)
const calculateProductSummaryInternal = (product: ProductItem) => {
  const mainProductMsrp = Number(product.msrp.value) || 0;
  const mainProductSellingPrice = Number(product.requestSellingPrice.value) || 0;

  const subTotals = product.subProducts.reduce((acc, sub) => {
    acc.totalMsrp += Number(sub.msrp.value) || 0;
    acc.totalSellingPrice += Number(sub.requestSellingPrice.value) || 0;
    return acc;
  }, { totalMsrp: 0, totalSellingPrice: 0 });

  const totalMsrp = mainProductMsrp + subTotals.totalMsrp;
  const totalSellingPrice = mainProductSellingPrice + subTotals.totalSellingPrice;
  const avgPercent = totalMsrp > 0 ? (totalSellingPrice / totalMsrp * 100) : 0;

  return {
    totalMsrp,
    totalSellingPrice,
    avgPercent: isNaN(avgPercent) ? '0.00' : avgPercent.toFixed(2)
  };
};

// Watch for changes in products to ensure calculations are up to date
// This might be heavy if not needed, direct calls are often better.
// Consider if this is necessary or if @update:model-value on inputs is sufficient.
// watch(() => props.products, (newProducts) => {
//   newProducts.forEach(p => {
//     updateSellingPriceInternal(p);
//     p.subProducts.forEach(sp => updateSellingPriceInternal(sp));
//   });
// }, { deep: true });

</script>

<template>
  <v-card variant="outlined" class="mb-6">
    <v-card-title class="text-subtitle-1 bg-grey-lighten-4 d-flex justify-space-between align-center">
      <span>{{ categoryTitle }}</span>
      <v-btn v-if="isCondensedView" color="primary" variant="text" @click="emit('addProduct')" size="small">
        <v-icon start icon="mdi-plus-circle-outline"></v-icon>
        Add Item
      </v-btn>
    </v-card-title>

    <!-- Condensed View -->
    <template v-if="isCondensedView">
      <v-table density="compact" class="mb-0">
        <thead>
          <tr>
            <th class="text-left" style="min-width: 180px;">Item</th>
            <th class="text-left">Item Number</th>
            <th class="text-left">DSD Qty</th>
            <th class="text-left">Dealer ITs</th>
            <th class="text-left">Selling Price</th>
            <th class="text-left">MSRP</th>
            <th class="text-left">% of MSRP</th>
            <th class="text-center" style="min-width: 150px;">Actions</th>
          </tr>
        </thead>
        <tbody>
          <template v-if="products.length === 0">
            <tr>
              <td colspan="8" class="text-center pa-4">No {{ categoryType }} items added.</td>
            </tr>
          </template>
          <template v-for="(product, productIndex) in products" :key="`${categoryType}-condensed-${productIndex}`">
            <tr :class="{'bg-grey-lighten-5': productIndex % 2 !== 0}">
              <td>
                <v-autocomplete
                  v-model="product.productName.value"
                  :items="productOptions"
                  item-title="title"
                  item-value="value"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => updateProductDetailsInternal(product, val as string)"
                ></v-autocomplete>
              </td>
              <td>
                <v-text-field
                  v-model="product.itemNumber.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.dsdQuantity.value"
                  type="number"
                  :min="1"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => handleDsdQuantityChange(product, val as string | number)"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.dealerITsQuantity.value"
                  type="number"
                  :min="0"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => handleDealerItsQuantityChange(product, val as string | number)"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.requestSellingPrice.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.msrp.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.percentOfMsrp.value"
                  type="number"
                  min="1"
                  max="100"
                  density="compact"
                  variant="plain"
                  hide-details
                  suffix="%"
                  @update:model-value="handlePercentChangeInternal(product)"
                ></v-text-field>
              </td>
              <td class="text-center">
                <v-btn icon="mdi-delete" variant="text" color="error" size="small" @click="emit('removeProduct', productIndex)"></v-btn>
                <v-btn icon="mdi-plus-box-multiple-outline" variant="text" color="primary" size="small" @click="emit('addSubProduct', productIndex)" title="Add Accessory"></v-btn>
              </td>
            </tr>
            <!-- Sub-products condensed view -->
            <tr v-for="(subProduct, subIndex) in product.subProducts" :key="`${categoryType}-sub-condensed-${productIndex}-${subIndex}`" class="bg-blue-lighten-5">
              <td class="pl-8">
                <v-autocomplete
                  v-model="subProduct.productName.value"
                  :items="getSubProductOptionsFunc(product.productName.value as string)"
                  item-title="title"
                  item-value="value"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => updateProductDetailsInternal(subProduct, val as string)"
                  prepend-inner-icon="mdi-subdirectory-arrow-right"
                ></v-autocomplete>
              </td>
              <td>
                <v-text-field
                  v-model="subProduct.itemNumber.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="subProduct.dsdQuantity.value"
                  type="number"
                  :min="1"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => handleDsdQuantityChange(subProduct, val as string | number)"
                ></v-text-field>
              </td>
               <td>
                <v-text-field
                  v-model="subProduct.dealerITsQuantity.value"
                  type="number"
                  :min="0"
                  density="compact"
                  variant="plain"
                  hide-details
                  @update:model-value="(val) => handleDealerItsQuantityChange(subProduct, val as string | number)"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="subProduct.requestSellingPrice.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="subProduct.msrp.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="subProduct.percentOfMsrp.value"
                  type="number"
                  min="1"
                  max="100"
                  density="compact"
                  variant="plain"
                  hide-details
                  suffix="%"
                  @update:model-value="handlePercentChangeInternal(subProduct)"
                ></v-text-field>
              </td>
              <td class="text-center">
                <v-btn icon="mdi-delete" variant="text" color="error" size="small" @click="emit('removeSubProduct', { productIndex, subProductIndex: subIndex })"></v-btn>
              </td>
            </tr>
          </template>
        </tbody>
      </v-table>
    </template>

    <!-- Expanded View -->
    <template v-else>
      <v-card-text>
        <template v-if="products.length === 0">
            <p class="text-center pa-4">No {{ categoryType }} items added.</p>
        </template>
        <template v-for="(product, productIndex) in products" :key="`${categoryType}-expanded-${productIndex}`">
          <div class="product-container mb-6">
            <div class="product-header pa-2 d-flex justify-space-between align-center" :class="{'bg-grey-lighten-5': productIndex % 2 !== 0}">
              <h3 class="text-subtitle-1">Main Unit {{ productIndex + 1 }}</h3>
              <div>
                <v-btn size="small" color="primary" @click="emit('addSubProduct', productIndex)" prepend-icon="mdi-plus-box-multiple-outline" class="mr-2">
                  {{ t('page.sales_request_form.worksheets.buttons.add_accessory') }}
                </v-btn>
                <v-btn size="small" color="error" @click="emit('removeProduct', productIndex)" prepend-icon="mdi-delete">
                  {{ t('page.sales_request_form.worksheets.buttons.remove_main_unit') }}
                </v-btn>
              </div>
            </div>

            <v-row class="pa-4">
              <v-col cols="12" md="6" lg="4">
                <v-autocomplete
                  v-model="product.productName.value"
                  :items="productOptions"
                  :label="t('page.sales_request_form.worksheets.fields.product_name.label')"
                  item-title="title"
                  item-value="value"
                  density="compact"
                  variant="outlined"
                  hide-details="auto"
                  @update:model-value="(val) => updateProductDetailsInternal(product, val as string)"
                ></v-autocomplete>
              </v-col>
              <v-col cols="12" md="6" lg="2">
                <v-text-field
                  v-model="product.itemNumber.value"
                  :label="t('page.sales_request_form.worksheets.fields.item_number.label')"
                  density="compact"
                  variant="outlined"
                  readonly
                  hide-details="auto"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6" lg="2">
                <v-text-field
                  v-model="product.dsdQuantity.value"
                  :label="t('page.sales_request_form.worksheets.fields.dsd_quantity.label')"
                  type="number"
                  :min="1"
                  density="compact"
                  variant="outlined"
                  hide-details="auto"
                  @update:model-value="(val) => handleDsdQuantityChange(product, val as string | number)"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6" lg="2">
                <v-text-field
                  v-model="product.dealerITsQuantity.value"
                  :label="t('page.sales_request_form.worksheets.fields.dealer_its_quantity.label')"
                  type="number"
                  :min="0"
                  density="compact"
                  variant="outlined"
                  hide-details="auto"
                  @update:model-value="(val) => handleDealerItsQuantityChange(product, val as string | number)"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6" lg="2">
                <v-text-field
                  v-model="product.msrp.value"
                  :label="t('page.sales_request_form.worksheets.fields.msrp.label')"
                  density="compact"
                  variant="outlined"
                  readonly
                  prefix="$"
                  hide-details="auto"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6" lg="3">
                <v-text-field
                  v-model="product.percentOfMsrp.value"
                  :label="t('page.sales_request_form.worksheets.fields.percent_of_msrp.label')"
                  type="number"
                  min="1"
                  max="100"
                  suffix="%"
                  density="compact"
                  variant="outlined"
                  hide-details="auto"
                  @update:model-value="handlePercentChangeInternal(product)"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6" lg="3">
                <v-text-field
                  v-model="product.requestSellingPrice.value"
                  :label="t('page.sales_request_form.worksheets.fields.request_selling_price.label')"
                  density="compact"
                  variant="outlined"
                  readonly
                  prefix="$"
                  hide-details="auto"
                ></v-text-field>
              </v-col>
            </v-row>

            <!-- Sub Products Expanded -->
            <div v-if="product.subProducts.length > 0" class="sub-products ml-4 mt-2">
              <div v-for="(subProduct, subIndex) in product.subProducts" :key="`${categoryType}-exp-sub-${productIndex}-${subIndex}`" class="sub-product-container mb-4">
                <div class="sub-product-header pa-2 d-flex justify-space-between align-center" :class="{'bg-blue-lighten-5': subIndex % 2 === 0}">
                  <h4 class="text-subtitle-2">Accessory {{ subIndex + 1 }}</h4>
                  <v-btn size="small" color="error" @click="emit('removeSubProduct', { productIndex, subProductIndex: subIndex })" prepend-icon="mdi-delete">
                    {{ t('page.sales_request_form.worksheets.buttons.remove') }}
                  </v-btn>
                </div>
                <v-row class="pa-4">
                  <v-col cols="12" md="6" lg="4">
                    <v-autocomplete
                      v-model="subProduct.productName.value"
                      :items="getSubProductOptionsFunc(product.productName.value as string)"
                      :label="t('page.sales_request_form.worksheets.fields.product_name.label')"
                      item-title="title"
                      item-value="value"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="(val) => updateProductDetailsInternal(subProduct, val as string)"
                    ></v-autocomplete>
                  </v-col>
                  <v-col cols="12" md="6" lg="2">
                    <v-text-field
                      v-model="subProduct.itemNumber.value"
                      :label="t('page.sales_request_form.worksheets.fields.item_number.label')"
                      density="compact"
                      variant="outlined"
                      readonly
                      hide-details="auto"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="2">
                    <v-text-field
                      v-model="subProduct.dsdQuantity.value"
                      :label="t('page.sales_request_form.worksheets.fields.dsd_quantity.label')"
                      type="number"
                      :min="1"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="(val) => handleDsdQuantityChange(subProduct, val as string | number)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="2">
                     <v-text-field
                        v-model="subProduct.dealerITsQuantity.value"
                        :label="t('page.sales_request_form.worksheets.fields.dealer_its_quantity.label')"
                        type="number"
                        :min="0"
                        density="compact"
                        variant="outlined"
                        hide-details="auto"
                        @update:model-value="(val) => handleDealerItsQuantityChange(subProduct, val as string | number)"
                      ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="2">
                    <v-text-field
                      v-model="subProduct.msrp.value"
                      :label="t('page.sales_request_form.worksheets.fields.msrp.label')"
                      density="compact"
                      variant="outlined"
                      readonly
                      prefix="$"
                      hide-details="auto"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="3">
                    <v-text-field
                      v-model="subProduct.percentOfMsrp.value"
                      :label="t('page.sales_request_form.worksheets.fields.percent_of_msrp.label')"
                      type="number"
                      min="1"
                      max="100"
                      suffix="%"
                      density="compact"
                      variant="outlined"
                      hide-details="auto"
                      @update:model-value="handlePercentChangeInternal(subProduct)"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6" lg="3">
                    <v-text-field
                      v-model="subProduct.requestSellingPrice.value"
                      :label="t('page.sales_request_form.worksheets.fields.request_selling_price.label')"
                      density="compact"
                      variant="outlined"
                      readonly
                      prefix="$"
                      hide-details="auto"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </div>
            </div>

            <!-- Product Summary (Expanded View Only) -->
            <v-row class="mt-4 pa-2 bg-grey-lighten-4">
              <v-col cols="12">
                <h4 class="text-subtitle-1 mb-2">Summary for Main Unit {{ productIndex + 1 }} & Accessories</h4>
              </v-col>
              <v-col cols="12" md="4">
                <p><strong>Total MSRP:</strong> ${{ calculateProductSummaryInternal(product).totalMsrp.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
              </v-col>
              <v-col cols="12" md="4">
                <p><strong>Total Selling Price:</strong> ${{ calculateProductSummaryInternal(product).totalSellingPrice.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2}) }}</p>
              </v-col>
              <v-col cols="12" md="4">
                <p><strong>Avg. % of MSRP:</strong> {{ calculateProductSummaryInternal(product).avgPercent }}%</p>
              </v-col>
            </v-row>
          </div>
        </template>
        
        <div class="d-flex justify-end mt-4">
          <v-btn color="primary" @click="emit('addProduct')" prepend-icon="mdi-plus">
            {{ t('page.sales_request_form.worksheets.buttons.add_main_unit', { unit: categoryTitle === 'Solutions' ? 'Solution' : categoryTitle }) }}
          </v-btn>
        </div>
      </v-card-text>
    </template>
  </v-card>
</template>

<style scoped>
.product-container {
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden; /* Ensures border-radius is respected by children */
}

.product-header {
  /* background-color: #f5f5f5; Default, overridden by :class binding */
}

.sub-product-container {
  border: 1px solid #e0e0e0; /* Lighter border for sub-products */
  border-radius: 4px;
  /* background-color: #f9f9f9; Default, overridden by :class binding */
}

.sub-product-header {
  /* background-color: #e3f2fd; Default, overridden by :class binding */
}

.sub-products {
  padding-left: 16px; /* Indent sub-products slightly */
  border-left: 2px solid #cfd8dc; /* Visual cue for hierarchy */
}

/* Ensure plain text fields in condensed view don't have extra padding/borders */
.v-table--density-compact :deep(.v-text-field .v-field__input) {
    padding-top: 0;
    padding-bottom: 0;
}
.v-table--density-compact :deep(.v-text-field .v-field) {
    box-shadow: none !important;
}
.v-table--density-compact :deep(.v-select .v-field__input) {
    padding-top: 0;
    padding-bottom: 0;
}
.v-table--density-compact :deep(.v-select .v-field) {
    box-shadow: none !important;
}
</style>
