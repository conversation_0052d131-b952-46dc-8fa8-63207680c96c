<script setup lang="ts">
import { defineProps, defineEmits, computed, ref, watch, onMounted } from 'vue';
import ServiceProductItem from './ServiceProductItem.vue';

const props = defineProps({
  defaultTermLength: {
    type: Number,
    required: true,
  },
  modelValue: { // Represents models_overview array
    type: Array,
    required: true,
    default: () => [],
  },
  selectedServiceValuePack: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(['update:modelValue']);

const localModelsOverview = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

const expandedPanels = ref<number[]>([]);

// Watch for changes in the modelValue prop to automatically expand all panels
onMounted(() => {
  if (Array.isArray(props.modelValue)) {
    expandedPanels.value = props.modelValue.map((_, index) => index);
  }
});

watch(() => props.modelValue, (newVal) => {
  if (Array.isArray(newVal)) {
    expandedPanels.value = newVal.map((_, index) => index);
  }
}, { deep: true, immediate: true });

</script>

<template>
  <v-card class="mb-4">
    <v-card-title>Product/Hardware Details</v-card-title>
    <v-card-text>
            <v-expansion-panels v-model="expandedPanels" multiple>
        <v-expansion-panel 
          v-for="(product, index) in localModelsOverview" 
          :key="(product as any).id"
          :value="index"
          variant="outlined"
          class="mb-4"
        >
          <v-expansion-panel-title>
            <!-- Display model name in the title if available -->
            <span class="text-subtitle-1">{{ `${(product as any).model}` || `Product ${index + 1}` }}</span>
          </v-expansion-panel-title>
          <v-expansion-panel-text>
           <span v-if="(product as any).parentModelName"> Main Product: <span class="text-subtitle-1 text-grey mb-2">{{ (product as any).parentModelName }}</span></span>

                                    <ServiceProductItem 
              :model-value="localModelsOverview[index]"
              @update:model-value="newValue => localModelsOverview[index] = newValue"
              :selected-service-value-pack="selectedServiceValuePack"
              :default-term-length="props.defaultTermLength"
            />
          </v-expansion-panel-text>
        </v-expansion-panel>
      </v-expansion-panels>

    </v-card-text>

    

    <!-- Static Notes -->
    <v-card-text class="pt-0">
      <v-alert density="compact" type="info" variant="tonal" class="mb-2">
        *Minimum Base Charges are Monthly For imagePRESS and Quarterly for MFPs.
      </v-alert>
      <v-alert density="compact" type="info" variant="tonal" v-if="selectedServiceValuePack === 'Gold'">
        Service Value Pack (Gold) Includes: Break & Fix services, IWR (Automated Toner & Service Alert).
        <!-- Add similar alerts for Silver, Bronze if content differs and is known -->
      </v-alert>
      <!-- Placeholder for other SVP packs if needed -->
      <v-alert density="compact" type="info" variant="tonal" v-else-if="selectedServiceValuePack === 'Silver'">
        Service Value Pack (Silver) Includes: ... (Define content if different)
      </v-alert>
       <v-alert density="compact" type="info" variant="tonal" v-else-if="selectedServiceValuePack === 'Bronze'">
        Service Value Pack (Bronze) Includes: ... (Define content if different)
      </v-alert>
    </v-card-text>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
</style>
