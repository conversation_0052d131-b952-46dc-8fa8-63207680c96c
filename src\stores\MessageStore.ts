/**
 * @file Pinia store defining global state for messages that occur in the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { defineStore } from 'pinia';
import type { AppMessage, MessageStoreState } from '@/lib/common/types';

/**
 * ------
 * Export
 * ------
 */

export const useMessageStore = defineStore( 'MessageStore',
{
    /**
	 * Store state variables.
	 */
    state : () : MessageStoreState =>
    {
        return {
            messageQueue: [],
            showMessage: false,
            disableApp: false
        }
    },

    /**
	 * Store actions.
	 */
    actions :
    {
        /**
         * Display the message using the provided parameters.
         * 
         * @param { AppMessage } [messageDetails] - message object.
         */
        show ( messageDetails : AppMessage ) : void
        {
            // Add message to the queue to display.
            this.messageQueue.push( messageDetails );
        },

        /**
         * Hides the message and re-enables application (if disabled).
         */
        hide ()
        {
            // Remove the first message in the queue.
            this.messageQueue.shift();

            this.showMessage = false;
            this.disableApp = false;
        }
    }
}); 