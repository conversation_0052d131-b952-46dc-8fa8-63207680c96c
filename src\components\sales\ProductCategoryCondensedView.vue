<script setup lang="ts">
import { PropType } from 'vue';

// Define the type for a product item field (mirroring parent's structure)
interface ProductField {
  type: string;
  value: string | number;
  readonly?: boolean;
  min?: number;
  max?: number;
}

// Define the type for a product item (mirroring parent's structure)
interface ProductItem {
  productName: ProductField;
  dsdQuantity: ProductField;
  dealerITsQuantity: ProductField;
  itemNumber: ProductField;
  requestSellingPrice: ProductField;
  msrp: ProductField;
  percentOfMsrp: ProductField;
  subProducts: ProductItem[];
}

const props = defineProps({
  title: {
    type: String,
    required: true,
  },
  products: {
    type: Array as PropType<ProductItem[]>,
    required: true,
  },
  productType: {
    type: String as PropType<'hardware' | 'software'>,
    required: true,
  },
  productOptions: {
    type: Array as PropType<{ title: string; value: string }[]>,
    required: true,
  },
  getSubProductOptionsFunc: {
    type: Function as PropType<(parentValue: string) => { title: string; value: string }[]>,
    required: true,
  },
  updateProductDetailsFunc: {
    type: Function as PropType<(product: ProductItem, productValue: string, isSubProduct?: boolean) => void>,
    required: true,
  },
  handlePercentChangeFunc: {
    type: Function as PropType<(product: ProductItem) => void>,
    required: true,
  },
  removeProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', index: number) => void>,
    required: true,
  },
  addSubProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', productIndex: number) => void>,
    required: true,
  },
  removeSubProductFunc: {
    type: Function as PropType<(type: 'hardware' | 'software', productIndex: number, subProductIndex: number) => void>,
    required: true,
  },
  addProductToListFunc: { // Renamed from addProduct to avoid potential naming conflicts if emitting
    type: Function as PropType<(type: 'hardware' | 'software') => void>,
    required: true,
  },
  t: {
    type: Function as PropType<(key: string) => string>,
    required: true,
  },
});

const onDsdQuantityChange = (product: ProductItem, val: string | number) => {
  product.dsdQuantity.value = Math.max(1, Number(val) || 1);
};

const onDealerItsQuantityChange = (product: ProductItem, val: string | number) => {
  product.dealerITsQuantity.value = Math.max(0, Number(val) || 0);
};

</script>

<template>
  <v-card variant="outlined" class="mb-6">
    <v-card-title class="text-subtitle-1 bg-grey-lighten-4">{{ props.title }}</v-card-title>
    <v-table density="compact" class="mb-0">
      <thead>
        <tr>
          <th class="text-left" style="min-width: 200px;">{{ props.t('page.sales_request_form.worksheets.columns.item') }}</th>
          <th class="text-left" style="min-width: 100px;">{{ props.t('page.sales_request_form.worksheets.columns.item_number') }}</th>
          <th class="text-left" >{{ props.t('page.sales_request_form.worksheets.columns.dsd_qty') }}</th>
          <th class="text-left" >{{ props.t('page.sales_request_form.worksheets.columns.dealer_its') }}</th>
          <th class="text-left" style="min-width: 120px;">{{ props.t('page.sales_request_form.worksheets.columns.selling_price') }}</th>
          <th class="text-left" style="min-width: 120px;">{{ props.t('page.sales_request_form.worksheets.columns.msrp') }}</th>
          <th class="text-left" style="min-width: 120px;">{{ props.t('page.sales_request_form.worksheets.columns.percent_msrp') }}</th>
          <!-- <th class="text-center" style="min-width: 150px;">{{ props.t('page.sales_request_form.worksheets.columns.actions') }}</th> -->
        </tr>
      </thead>
      <tbody>
        <template v-if="props.products.length === 0">
          <tr>
            <td colspan="8" class="text-center pa-4">{{ props.t('page.sales_request_form.worksheets.messages.no_items_added', { item_type: props.title.toLowerCase() }) }}</td>
          </tr>
        </template>
        <template v-for="(product, productIndex) in props.products" :key="`${props.productType}-condensed-${productIndex}`">
          <!-- Main product row -->
          <tr :class="{'bg-grey-lighten-4': productIndex % 2 === 0}">
            <td>
              <v-autocomplete
                v-model="product.productName.value"
                :items="props.productOptions"
                item-title="title"
                item-value="value"
                density="compact"
                variant="plain"
                hide-details
                readonly
                @update:model-value="(val: string) => props.updateProductDetailsFunc(product, val)"
              ></v-autocomplete>
            </td>
            <template v-if="props.productType !== 'software'">
            <td>
              <v-text-field
                v-model="product.itemNumber.value"
                density="compact"
                variant="plain"
                hide-details
                readonly
              ></v-text-field>
            </td>
           
              <td>
                <v-text-field
                  v-model="product.dsdQuantity.value"
                  type="number"
                  :min="1"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  @update:model-value="(val) => onDsdQuantityChange(product, val)"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.dealerITsQuantity.value"
                  type="number"
                  :min="0"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  @update:model-value="(val) => onDealerItsQuantityChange(product, val)"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.requestSellingPrice.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.msrp.value"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  prefix="$"
                ></v-text-field>
              </td>
              <td>
                <v-text-field
                  v-model="product.percentOfMsrp.value"
                  type="number"
                  min="1"
                  max="100"
                  density="compact"
                  variant="plain"
                  hide-details
                  readonly
                  suffix="%"
                  @update:model-value="props.handlePercentChangeFunc(product)"
                ></v-text-field>
              </td>
            </template>
            <template v-else>
              <td colspan="5"></td>
            </template>
            <!-- <td class="text-center">
              <v-btn size="x-small" icon color="error" variant="text" @click="props.removeProductFunc(props.productType, productIndex)" :title="props.t('page.sales_request_form.worksheets.buttons.remove_main_item')">
                <v-icon>delete</v-icon>
              </v-btn>
              <v-btn size="x-small" icon color="secondary" variant="text" @click="props.addSubProductFunc(props.productType, productIndex)" :title="props.productType === 'software' ? props.t('page.sales_request_form.worksheets.buttons.add_solution_item') : props.t('page.sales_request_form.worksheets.buttons.add_accessory')">
                <v-icon>add</v-icon>
              </v-btn>
            </td> -->
          </tr>
          <!-- Sub-product rows -->
          <tr v-for="(subProduct, subIndex) in product.subProducts" :key="`${props.productType}-sub-condensed-${productIndex}-${subIndex}`" class="bg-blue-lighten-5">
            <td class="pl-4">
              <v-autocomplete
                v-model="subProduct.productName.value"
                :items="props.getSubProductOptionsFunc(product.productName.value.toString())"
                item-title="title"
                item-value="value"
                density="compact"
                variant="plain"
                hide-details
                readonly
                @update:model-value="(val: string) => props.updateProductDetailsFunc(subProduct, val, true)"
              ></v-autocomplete>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.itemNumber.value"
                density="compact"
                variant="plain"
                hide-details
                readonly
              ></v-text-field>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.dsdQuantity.value"
                type="number"
                :min="1"
                density="compact"
                variant="plain"
                hide-details
                readonly
                @update:model-value="(val) => onDsdQuantityChange(subProduct, val)"
              ></v-text-field>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.dealerITsQuantity.value"
                type="number"
                :min="0"
                density="compact"
                variant="plain"
                hide-details
                readonly
                @update:model-value="(val) => onDealerItsQuantityChange(subProduct, val)"
              ></v-text-field>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.requestSellingPrice.value"
                density="compact"
                variant="plain"
                hide-details
                readonly
                prefix="$"
              ></v-text-field>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.msrp.value"
                density="compact"
                variant="plain"
                hide-details
                readonly
                prefix="$"
              ></v-text-field>
            </td>
            <td>
              <v-text-field
                v-model="subProduct.percentOfMsrp.value"
                type="number"
                min="1"
                max="100"
                density="compact"
                variant="plain"
                hide-details
                readonly
                suffix="%"
                @update:model-value="props.handlePercentChangeFunc(subProduct)"
              ></v-text-field>
            </td>
            <!-- <td class="text-center">
              <v-btn size="x-small" icon color="error" variant="text" @click="props.removeSubProductFunc(props.productType, productIndex, subIndex)" :title="props.t('page.sales_request_form.worksheets.buttons.remove_accessory')">
                <v-icon>delete</v-icon>
              </v-btn>
              <v-btn size="x-small" icon disabled style="visibility: hidden;">
                <v-icon>add</v-icon> 
              </v-btn>
            </td> -->
          </tr>
        </template>
      </tbody>
    </v-table>
    <!-- <v-card-actions>
      <v-spacer></v-spacer>
      <v-btn color="primary" variant="text" @click="props.addProductToListFunc(props.productType)">
        <v-icon start icon="add_circle"></v-icon>
        {{ props.t('page.sales_request_form.worksheets.buttons.add_item', { item_type: props.title }) }}
      </v-btn>
    </v-card-actions> -->
  </v-card>
</template>

<style scoped>
/* Add any specific styles for this component if needed */
.v-table th {
  font-weight: bold;
}
.v-table td .v-text-field,
.v-table td .v-select {
  font-size: 0.875rem; /* Compact text */
}
</style>
