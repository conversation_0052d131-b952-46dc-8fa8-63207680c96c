/**
 * @file Canon Canada authentication class (main).
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { Configuration, AuthenticationResult, AccountInfo, SsoSilentRequest, RedirectRequest } from '@azure/msal-browser';
import type { ICanonAuth, MSGraphUser } from '@/lib/common/types';

import { CanonAuthState } from '@/lib/common/types';
import { LogLevel, PublicClientApplication, InteractionRequiredAuthError } from '@azure/msal-browser';
import Logger from '@/lib/logger/Logger';
import _ from 'lodash';

/**
 * ----
 * Main
 * ----
 */

/**
 * Canon Canada authentication class (main).
 *
 * @class CanonAuth
 */
class CanonAuth implements ICanonAuth
{
    public client: PublicClientApplication;

    public account: AccountInfo | null;

    public authState: CanonAuthState = CanonAuthState.NOT_READY;

    /**
     * Default login scopes.
     *
     * @private
     * @type {string[]}
     * @memberof CanonAuth
     */
    private loginScopes : string[] = [ 'openid', 'offline_access', 'email', 'profile', 'User.Read', 'User.Read.All' ];

    /**
     * Creates an instance of CanonAuth.
     * @memberof CanonAuth
     */
    constructor ()
    {
        // Initialize default account details
        this.account = null;

        // Initialize the MSAL public client.
        this.client = new PublicClientApplication( this.getConfig() );
    }

    /**
     * Returns the MSAL client configuration object.
     *
     * @private
     * @return {*}  {Configuration}
     * @memberof CanonAuth
     */
    private getConfig () : Configuration
    {
        return {
            auth :
            {
                clientId: import.meta.env.VITE_APP_AUTH_CLIENT_ID,
                authority: `https://login.microsoftonline.com/${ import.meta.env.VITE_APP_AUTH_TENANT_ID }/`,
                redirectUri: import.meta.env.VITE_APP_AUTH_REDIRECT_URL,
                postLogoutRedirectUri: '/'
            },

            cache :
            {
                cacheLocation: 'localStorage',
                temporaryCacheLocation: 'localStorage',
                claimsBasedCachingEnabled: true,
                secureCookies: true,
                storeAuthStateInCookie: true
            },

            system :
            {
                loggerOptions:
                {
                    loggerCallback: ( level: LogLevel, message: string, containsPii: boolean ) =>
                    {
                        if ( containsPii )
                        {
                            return;
                        }
                        switch ( level )
                        {
                            case LogLevel.Error:
                                Logger( message );
                                return;
                            case LogLevel.Info:
                                Logger( message );
                                return;
                            case LogLevel.Verbose:
                                Logger( message );
                                return;
                            case LogLevel.Warning:
                                Logger( message );
                                return;
                            default:
                                return;
                        }
                    },

                    logLevel: LogLevel.Error
                },
                
                allowRedirectInIframe: true
            }
        }
    }

    public async cleanSession () : Promise <void>
    {
        // Clean up MSAL cache.
        await this.client.clearCache();

        // Clear account details for this object.
        this.account = null;

        // Clean up application values.
        localStorage.removeItem( 'appTheme' );
        localStorage.removeItem( 'appLanguage' );
    }
    
    public async init () : Promise <void>
    {
        try
        {
            // Wait for the client to be ready.
            await this.client.initialize();

            // Process any pending redirect (login) tasks.
            const response : AuthenticationResult | null = await this.client.handleRedirectPromise();

            // Login event has occurred.
            if ( response !== null )
            {
                // Set the active account to the recently logged in account.
                this.client.setActiveAccount( response.account );

                // Set account.
                this.account = response.account;
            }
            // Check for previous session and set user if exists.
            else
            {
                // Get all the accounts current in browser cache.
                const currentAccounts = this.client.getAllAccounts();

                // No accounts / previous auth session.
                if ( !currentAccounts || currentAccounts.length < 1 )
                {
                    this.account = null;
                }
                // Single account detected.
                else if ( currentAccounts.length === 1 )
                {
                    // Set account in MSAL cache.
                    this.client.setActiveAccount( currentAccounts[0] );

                    // Set account.
                    this.account = currentAccounts[0];
                }
                // Multiple accounts detected - Edge case - Not Supported
                else
                {
                    Logger( currentAccounts, 'Multiple accounts detected. Clearing cache - Not supported.' );
                }
            }

            // Indicate a ready state.
            this.authState = CanonAuthState.READY;
        }
        catch ( e )
        {
            Logger( e, 'An error occurred while initializing auth ..' );

            // Indicate a ready state.
            this.authState = CanonAuthState.UNKNOWN_ERROR;
        }
    }

    public isAuthenticated () : boolean
    {
        const activeAccount : AccountInfo | null = this.client.getActiveAccount();

        if ( activeAccount !== null && this.account !== null )
        {
            return true;
        }

        return false;
    }

    public getRoles () : string[] | null
    {
        const activeAccount : AccountInfo | null = this.client.getActiveAccount();

        let roles : string[] | null = null;

        if ( activeAccount !== null && this.account !== null )
        {
            if ( activeAccount.idTokenClaims?.roles )
            {
                roles = activeAccount.idTokenClaims.roles;
            }
        }

        return roles;
    }

    public hasAllRoles ( roles : string[] ) : boolean
    {
        const activeAccount : AccountInfo | null = this.client.getActiveAccount();

        if ( activeAccount !== null && activeAccount.idTokenClaims?.roles )
        {
            // Create array of the roles the user does not have.
            const roleDifference = _.difference( roles, activeAccount.idTokenClaims.roles );

            // If the user has all the roles required, this array should be empty.
            if ( roleDifference.length === 0 )
            {
                return true;
            }
        }

        return false;
    }

    public hasAnyRoles ( roles : string[] ) : boolean
    {
        const activeAccount : AccountInfo | null = this.client.getActiveAccount();

        if ( activeAccount !== null && activeAccount.idTokenClaims?.roles )
        {
            // Create array of the roles the user does not have.
            const roleDifference = _.difference( roles, activeAccount.idTokenClaims.roles );

            // If the user has any of the roles required, this array should smaller than the number of roles required.
            if ( roleDifference.length < roles.length )
            {
                return true;
            }
        }

        return false;
    }

    public async doLoginSSO () : Promise <boolean>
    {
        const requestParam : SsoSilentRequest = 
        {
            scopes: this.loginScopes
        }

        if ( this.account !== null )
        {
            // Creates a copy of the account object as it will be cleared later.
            requestParam.account = _.cloneDeep( this.account );
        }

        try
        {
            // Clear existing cache to ensure we have a clean slate - This application does not support multiple logins.
            await this.cleanSession();

            // Check if user is logged in already to Microsoft (SSO).
            const silentResponse : AuthenticationResult = await this.client.ssoSilent( requestParam );

            // SSO session was found.
            if ( silentResponse )
            {
                const userRoles = silentResponse.account?.idTokenClaims?.roles || null;

                // User has no roles assigned.
                if ( userRoles )
                {
                    // Set the active account to the recently logged in account.
                    this.client.setActiveAccount( silentResponse.account );

                    // Set account.
                    this.account = silentResponse.account;

                    return true;
                }
                else
                {
                    this.authState = CanonAuthState.NO_ACCESS;
                }
            }
        }
        catch ( e )
        {
            await this.cleanSession();

            if ( e instanceof InteractionRequiredAuthError )
            {
                // User has not been granted access to the application.
                if ( e.errorMessage.includes( 'not a direct member of a group with access' ) )
                {
                    this.authState = CanonAuthState.NO_ACCESS;
                }
            }
        }

        // Clean session as proper role not detected (even if SSO was successful).
        await this.cleanSession();

        return false;
    }

    public async doLoginRedirect () : Promise <boolean>
    {
        const requestParam : RedirectRequest = 
        {
            scopes: this.loginScopes
        }
        
        try
        {
            // This code will redirect the user to Microsoft login page.
            await this.client.loginRedirect( requestParam );

            // Update auth state.
            this.authState = CanonAuthState.REDIRECTED;

            return true;
        }
        catch ( e )
        {
            this.authState = CanonAuthState.UNKNOWN_ERROR;
        }

        return false;
    }
    
    public async doLogin () : Promise <boolean>
    {
        // First, try SSO sign-in.
        const ssoResult : boolean = await this.doLoginSSO();

        // SSO was successful.
        if ( ssoResult )
        {
            return true;
        }
        // SSO failed.
        else
        {
            if ( this.authState === CanonAuthState.NO_ACCESS )
            {
                return false;
            }
            else
            {
                return await this.doLoginRedirect();
            }
        }
    }

    public async doLogout () : Promise <boolean>
    {
        await this.cleanSession();

        return true;
    }

    public async getToken ( scopes? : string[], refresh : boolean = false ) : Promise <AuthenticationResult | null>
    {
        try
        {
            const tokenResult : AuthenticationResult = await this.client.acquireTokenSilent
            ({
                scopes: scopes || this.loginScopes,
                forceRefresh: refresh
            });

            return tokenResult;
        }
        catch ( e )
        {
            Logger( e, 'Error getting token (getToken):' );
        }

        return null;
    }

    public decodeToken ( token : string ) : any
    {
        try
        {
            const parts = token.split('.');

            // Ensure there are 3 components (header, payload and signature).
            if ( parts.length !== 3 )
            {
                throw new Error( 'Invalid JWT format' );
            }
        
            const decodedHeader = JSON.parse(atob(parts[0]));
            const decodedPayload = JSON.parse(atob(parts[1]));

            return {
                header: decodedHeader,
                payload: decodedPayload
            };
        }
        catch ( e )
        {
            Logger( e, 'Error decoding token.' );
        }
    }
    public getUsersList=async (searchTerm:string) : Promise <MSGraphUser[]>=>{
        const tokenResult = await this.getToken( undefined, false );
    
        const response = await fetch(
            `https://graph.microsoft.com/v1.0/users?$filter=startswith(mailNickname,'${searchTerm}') or startswith(displayName,'${searchTerm}') or startswith(mail,'${searchTerm}')&$select=id,displayName,mail,mailNickname,jobTitle,mobilePhone,department,employeeId,address,office,manager&$expand=manager`,
            {
                headers: {
                    Authorization: `Bearer ${tokenResult?.accessToken}`,
                },
            }
        );
    
        if (!response.ok) {
            throw new Error("Error fetching users");
        }
    
        const data = await response.json();
        return data.value as MSGraphUser[];
    }
}

// Instantiation of singleton object.
const _CanonAuth = new CanonAuth();

/**
 * -------
 * Exports
 * -------
 */

export { _CanonAuth as CanonAuth };