/**
 * @file Primary entry file to application. Initializes application, Vue and plugins.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { createApp } from 'vue';
import { createPinia } from 'pinia';
import { createVuetify } from 'vuetify';
import { aliases, md } from 'vuetify/iconsets/md';
import { CanonAuth } from '@/lib/canonAuth';
import { CanonAuthNavigation } from '@/lib/canonAuth';
import { CanonAuthGuard } from '@/lib/canonAuth';
import { appThemeLight } from '@/styles/themes/light';
import { appThemeDark } from '@/styles/themes/dark';
import { createI18n } from 'vue-i18n';

import App from './App.vue';
import router from './router';
import enUS from '@/locale/en-US.json';
import frCA from '@/locale/fr-CA.json';

import 'vuetify/styles';
import "@/styles/general.scss";

/**
 * ----
 * Main
 * ----
 */

/**
 * Starts the application. This function is necessary in order to ensure
 * asynchronous processes are properly waited for.
 */
const init = async () =>
{
    /**
     * ----
     * Auth
     * 
     * Initializes the MSAL authentication library, checks
     * for a login request and sets previous user sessions.
     * ----
     */

    await CanonAuth.init();

    /**
     * -----
     * VueJS
     * -----
     */

    // Initialize the Vue application.
    const app = createApp( App );

    /**
     * -----------------------------
     * Language Internationalization
     * -----------------------------
     * 
     * Configures Vue I18n for language support.
     */

    // Check if a language preference is already set.
    let cacheLanguage = localStorage.getItem( 'appLanguage' ) || 'en-US';

    // Create I18n configuration object.
    const i18n = createI18n
    ({
        legacy: false,
        locale: cacheLanguage,
        fallbackLocale: 'en-US',
        warnHtmlInMessage: false,
        warnHtmlMessage: false,
        globalInjection: true,
        messages:
        {
            'en-US': enUS,
            'fr-CA': frCA
        }
    })

    // Initialize language support.
    app.use( i18n );

    /**
     * -----
     * Pinia
     * -----
     */
    
    // Create a plugin for Pinia that makes the language object available to every store.
    const piniaLanguagePlugin : any = () =>
    {
        return {
            i18n: i18n
        }
    }

    // Init the Pinia instance.
    const pinia = createPinia();

    // Add the plugin for language.
    pinia.use( piniaLanguagePlugin );

    // Initialize Pinia state management.
    app.use( pinia );

    /**
     * ----------
     * Vue Router
     * 
     * Initializes the Vue Router object and installs special
     * routing logic that MSAL can use for Single Page Apps.
     * ----------
     */

    // Install authentication guard for routes.
    CanonAuthGuard.install( router, i18n );

    // Initialize routing management.
    app.use( router )

    // Create a custom navigation client for authentication calls.
    const authNavigation = new CanonAuthNavigation( router );

    // Register the navigation client in MSAL. This allows MSAL to leverage the Vue Router
    // to manipulate the browser's active route and URL for redirects.
    CanonAuth.client.setNavigationClient( authNavigation );

    /**
     * -------
     * Vuetify
     * 
     * Configures Vuetify (theme, font, etc.) and initializes.
     * -------
     */

    // Configure Vuetify options.
    const vuetify = createVuetify
    ({
        theme:
        {
            defaultTheme: 'appThemeLight',

            themes:
            {
                appThemeLight,
                appThemeDark
            }
        },

        icons: 
        {
            defaultSet: 'md',
            aliases,
            sets:
            {
                md
            },
        }
    });

    // Initialize elemental framework.
    app.use( vuetify );

    /**
     * -------
     * Mount
     * -------
     */

    // Mount and attach the Vue object to the browser.
    app.mount( '#app' );
}

// Start application.
init();