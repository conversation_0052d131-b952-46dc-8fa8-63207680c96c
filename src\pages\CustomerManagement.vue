<template>
	<div class="customer-management-page">
		<v-container fluid>
			<v-row>
				<v-col cols="12">
					<v-card>
						<v-card-title class="d-flex align-center justify-space-between">
							<div class="d-flex align-center">
								<v-icon class="mr-3" size="32">business</v-icon>
								<span class="text-h4">Customer Management</span>
							</div>
							<v-btn
								color="primary"
								prepend-icon="add"
								@click="handleAction('add', {} as Customer)"
							>
								Add Customer
							</v-btn>
						</v-card-title>
						
						<v-card-text>
							<v-table class="elevation-1">
								<thead>
									<tr class="customer-header">
										<th v-for="header in customerHeaders" :key="header.key" class="text-left pa-3 font-weight-bold bg-blue-grey-lighten-4">
											{{ header.title }}
										</th>
									</tr>
								</thead>
								<tbody v-if="loading">
									<tr>
										<td :colspan="customerHeaders.length" class="text-center pa-4">
											<v-progress-circular indeterminate color="primary"></v-progress-circular>
											<div class="mt-2">Loading customers...</div>
										</td>
									</tr>
								</tbody>
								<tbody v-else>
									<template v-for="(item, index) in flattenedData" :key="isCustomer(item) ? item.customerId : item.locationId">
										<!-- Customer Row -->
										<tr v-if="isCustomer(item)" class="customer-row">
											<td v-for="header in customerHeaders" :key="header.key" class="pa-3">
												<template v-if="header.key === 'businessName'">
													<div class="d-flex align-center">
														<v-btn
															:icon="isLoadingSubLocations(item) ? 'hourglass_empty' : (isExpanded(item) ? 'expand_less' : 'expand_more')"
															variant="text"
															size="small"
															:loading="isLoadingSubLocations(item)"
															@click="toggleExpansion(item)"
														></v-btn>
														<span class="ml-2 font-weight-medium">{{ item.businessName }}</span>
													</div>
												</template>
												<template v-else-if="header.key === 'displayName'">
													{{ item.displayName || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'customerCode'">
													{{ item.customerCode || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'status'">
													<v-chip 
														:color="item.status === 'Active' ? 'success' : 'warning'" 
														size="small" 
														variant="tonal"
													>
														{{ item.status || 'Unknown' }}
													</v-chip>
												</template>
												<template v-else-if="header.key === 'region'">
													{{ item.region || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'customerType'">
													{{ item.customerType || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'website'">
													<a v-if="item.website" :href="item.website.startsWith('http') ? item.website : 'https://' + item.website" target="_blank" class="text-decoration-none">
														{{ item.website }}
													</a>
													<span v-else class="text-grey">N/A</span>
												</template>
												<template v-else-if="header.key === 'relationshipStatus'">
													<div>
														<div>{{ item.relationshipStatus || 'N/A' }}</div>
														<div class="text-caption text-grey">SF: {{ item.sfOpportunityId || 'N/A' }}</div>
													</div>
												</template>
												<template v-else-if="header.key === 'type'">
													<v-chip 
														:color="isLocation(item) ? 'blue-grey' : 'primary'" 
														size="small" 
														variant="tonal"
													>
														{{ isLocation(item) ? 'Location' : 'Customer' }}
													</v-chip>
												</template>
												<template v-else-if="header.key === 'actions'">
													<v-menu>
														<template v-slot:activator="{ props: menuProps }">
															<v-btn
																icon="more_vert"
																variant="text"
																size="small"
																v-bind="menuProps"
															></v-btn>
														</template>
														<v-list>
															<v-list-item
																prepend-icon="add_location"
																title="Add Location"
																@click="handleAction('add-location', item)"
															></v-list-item>
															<v-list-item
																prepend-icon="edit"
																title="Edit"
																@click="handleAction('edit', item)"
															></v-list-item>
															<v-list-item
																prepend-icon="delete"
																title="Delete"
																@click="handleAction('delete', item)"
															></v-list-item>
														</v-list>
													</v-menu>
												</template>
											</td>
										</tr>
										
										<!-- Location Header (show when customer has expanded locations) -->
										<tr v-if="isCustomer(item) && item.locations && item.locations.length > 0 && isExpanded(item)" class="location-header bg-blue-lighten-5">
											<th v-for="header in locationHeaders" :key="header.key" class="text-left pa-2 font-weight-bold text-blue-darken-2 text-body-2">
												{{ header.title }}
											</th>
										</tr>
										
										<!-- Location Rows -->
										<tr v-if="isLocation(item)" class="location-row bg-blue-lighten-5 hover:bg-blue-lighten-4">
											<td v-for="header in locationHeaders" :key="header.key" class="pa-2 text-blue-darken-3">
												<template v-if="header.key === 'displayName'">
													<div class="d-flex align-center" style="padding-left: 24px;">
														<v-icon size="small" class="mr-2 text-grey">subdirectory_arrow_right</v-icon>
														<span>{{ item.displayName || 'Unnamed Location' }}</span>
													</div>
												</template>
												<template v-else-if="header.key === 'locationType'">
													<v-chip size="small" variant="tonal" color="blue-grey">
														{{ item.locationType || 'Office' }}
													</v-chip>
												</template>
												<template v-else-if="header.key === 'isPrimary'">
													<v-chip 
														:color="item.isPrimary === 'Y' ? 'primary' : 'default'"
														size="small" 
														variant="tonal"
													>
														{{ item.isPrimary === 'Y' ? 'Primary' : 'Secondary' }}
													</v-chip>
												</template>
												<template v-else-if="header.key === 'address'">
													<div>
														<div>{{ item.addressLine1 || 'N/A' }}</div>
														<div v-if="item.addressLine2" class="text-caption text-grey">{{ item.addressLine2 }}</div>
													</div>
												</template>
												<template v-else-if="header.key === 'city'">
													{{ item.city || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'state'">
													{{ item.state || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'postalCode'">
													{{ item.postalCode || 'N/A' }}
												</template>
												<template v-else-if="header.key === 'country'">
													{{ item.country || 'CA' }}
												</template>
												<template v-else-if="header.key === 'actions'">
													<v-menu>
														<template v-slot:activator="{ props: menuProps }">
															<v-btn
																icon="more_vert"
																variant="text"
																size="small"
																v-bind="menuProps"
															></v-btn>
														</template>
														<v-list>
															<v-list-item
																prepend-icon="edit"
																title="Edit"
																@click="handleAction('edit', item)"
															></v-list-item>
															<v-list-item
																prepend-icon="delete"
																title="Delete"
																@click="handleAction('delete', item)"
															></v-list-item>
														</v-list>
													</v-menu>
												</template>
											</td>
										</tr>
									</template>
								</tbody>
							</v-table>

							<!-- No Data Message -->
							<div v-if="!loading && flattenedData.length === 0" class="text-center pa-8">
								<v-icon size="48" color="grey-lighten-1">business</v-icon>
								<div class="text-h6 mt-2">No customers found</div>
								<v-btn color="primary" class="mt-2" @click="handleAction('add', {} as Customer)">
									Add First Customer
								</v-btn>
							</div>
						</v-card-text>
					</v-card>
				</v-col>
			</v-row>
		</v-container>

		<!-- Customer Dialog -->
		<v-dialog v-model="showCustomerDialog" max-width="600px" persistent>
			<v-card>
				<v-card-title>
					<span class="text-h5">{{ editingCustomer ? 'Edit Customer' : 'Add New Customer' }}</span>
				</v-card-title>
				<v-card-text>
					<v-container>
						<v-row>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.businessName"
									:error-messages="customerFormErrors.businessName"
									label="Business Name *"
									required
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.displayName"
									label="Display Name"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.legalName"
									label="Legal Name"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-text-field
									v-model="customerFormData.customerCode"
									label="Customer Code"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="customerFormData.status"
									:items="['Active', 'Inactive']"
									label="Status"
									density="compact"
								></v-select>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.sfOpportunityId"
									label="Salesforce Opportunity ID"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.relationshipStatus"
									label="Relationship Status"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-text-field
									v-model="customerFormData.customerType"
									label="Customer Type"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-text-field
									v-model="customerFormData.website"
									label="Website"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="customerFormData.region"
									label="Region"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="customerFormData.isGlobalAgreement"
									:items="[{title: 'Yes', value: 'Y'}, {title: 'No', value: 'N'}]"
									label="Global Agreement?"
									density="compact"
								></v-select>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="customerFormData.isSubsidiary"
									:items="[{title: 'Yes', value: 'Y'}, {title: 'No', value: 'N'}]"
									label="Subsidiary?"
									density="compact"
								></v-select>
							</v-col>
						</v-row>
					</v-container>
					<small>* indicates required field</small>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn color="blue-darken-2" variant="text" @click="closeCustomerDialog">
						Cancel
					</v-btn>
					<v-btn 
						color="blue-darken-1" 
						variant="elevated"
						:loading="customerFormLoading"
						@click="saveCustomer"
					>
						Save
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>

		<!-- Location Dialog -->
		<v-dialog v-model="showLocationDialog" max-width="600px" persistent>
			<v-card>
				<v-card-title>
					<span class="text-h5">{{ editingLocation ? 'Edit Location' : 'Add New Location' }}</span>
				</v-card-title>
				<v-card-text>
					<v-container>
						<v-row>
							<v-col cols="12">
								<v-text-field
									v-model="locationFormData.displayName"
									label="Location Name"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="locationFormData.locationType"
									:items="['Office', 'Warehouse', 'Branch', 'Store', 'Other']"
									label="Location Type"
									density="compact"
								></v-select>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="locationFormData.isPrimary"
									:items="[{title: 'Yes', value: 'Y'}, {title: 'No', value: 'N'}]"
									label="Primary Location?"
									density="compact"
								></v-select>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="locationFormData.addressLine1"
									:error-messages="locationFormErrors.addressLine1"
									label="Address Line 1 *"
									required
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="locationFormData.addressLine2"
									label="Address Line 2"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12">
								<v-text-field
									v-model="locationFormData.addressLine3"
									label="Address Line 3"
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-text-field
									v-model="locationFormData.city"
									:error-messages="locationFormErrors.city"
									label="City *"
									required
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="locationFormData.state"
									:items="['AB', 'BC', 'MB', 'NB', 'NL', 'NS', 'NT', 'NU', 'ON', 'PE', 'QC', 'SK', 'YT']"
									:error-messages="locationFormErrors.state"
									label="Province *"
									required
									density="compact"
								></v-select>
							</v-col>
							<v-col cols="12" md="6">
								<v-text-field
									v-model="locationFormData.postalCode"
									:error-messages="locationFormErrors.postalCode"
									label="Postal Code *"
									required
									density="compact"
								></v-text-field>
							</v-col>
							<v-col cols="12" md="6">
								<v-select
									v-model="locationFormData.country"
									:items="['CA', 'US']"
									label="Country"
									density="compact"
								></v-select>
							</v-col>
						</v-row>
					</v-container>
					<small>* indicates required field</small>
				</v-card-text>
				<v-card-actions>
					<v-spacer></v-spacer>
					<v-btn color="blue-darken-2" variant="text" @click="closeLocationDialog">
						Cancel
					</v-btn>
					<v-btn 
						color="blue-darken-1" 
						variant="elevated"
						:loading="locationFormLoading"
						@click="saveLocation"
					>
						Save
					</v-btn>
				</v-card-actions>
			</v-card>
		</v-dialog>
	</div>
</template>

<script setup lang="ts">
/**
 * @file Customer Management page for managing customers and their locations.
 * @version 1.0.0
 * @since 1.0.0
 */

import { ref, reactive, computed, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import CustomerManager from '@/components/sales/CustomerManager.vue';
import LocationManager from '@/components/sales/LocationManager.vue';
import { getCustomers, addCustomer as addCustomerApi, addLocation as addLocationApi, type Customer as ApiCustomer, type Location as ApiLocation, type AddCustomerPayload, type AddLocationPayload } from '@/services/salesRequestService';

// Add language support
const { t } = useI18n();



// Types for flattened data
type FlattenedItem = Customer | (Location & { _isSubLocation: true; _level: number });

// Type guards
const isCustomer = (item: FlattenedItem): item is Customer => {
	return !('_isSubLocation' in item);
};

const isLocation = (item: FlattenedItem): item is Location & { _isSubLocation: true; _level: number } => {
	return '_isSubLocation' in item;
};

// State
const customers = ref<Customer[]>([]);
const loading = ref(false);
const expandedCustomers = ref<Set<number>>(new Set());
const loadingSubLocations = ref<Set<number>>(new Set());

// Dialog states
const showCustomerDialog = ref(false);
const showLocationDialog = ref(false);
const editingCustomer = ref<Customer | null>(null);
const editingLocation = ref<Location | null>(null);
const selectedCustomerForLocation = ref<Customer | null>(null);

// Form states
const customerFormLoading = ref(false);
const customerFormError = ref('');
const locationFormLoading = ref(false);
const locationFormError = ref('');

// Customer Management Functions
const flattenedData = computed(() => {
	const result: FlattenedItem[] = [];
	
	customers.value.forEach(customer => {
		result.push(customer);
		
		if (customer._isExpanded && customer.locations) {
			customer.locations.forEach(location => {
				result.push({
					...location,
					_isSubLocation: true as const,
					_level: 1
				});
			});
		}
	});
	
	return result;
});

const isExpanded = (customer: Customer) => expandedCustomers.value.has(customer.customerId);
const isLoadingSubLocations = (customer: Customer) => loadingSubLocations.value.has(customer.customerId);

const toggleExpansion = async (customer: Customer) => {
	if (isExpanded(customer)) {
		expandedCustomers.value.delete(customer.customerId);
		customer._isExpanded = false;
	} else {
		expandedCustomers.value.add(customer.customerId);
		customer._isExpanded = true;
		
		// Load locations if not already loaded
		if (!customer.locations || customer.locations.length === 0) {
			await loadCustomerLocations(customer);
		}
	}
};

const loadCustomerLocations = async (customer: Customer) => {
	loadingSubLocations.value.add(customer.customerId);
	try {
		// In a real implementation, you would fetch locations from API
		// For now, we'll simulate with empty array or mock data
		customer.locations = customer.locations || [];
	} catch (error) {
		console.error('Error loading customer locations:', error);
	} finally {
		loadingSubLocations.value.delete(customer.customerId);
	}
};

const handleAction = (action: string, item: Customer | Location) => {
	if ('customerId' in item && !('_isSubLocation' in item)) {
		// Customer actions
		const customer = item as Customer;
		switch (action) {
			case 'add':
				openCustomerDialog();
				break;
			case 'edit':
				openCustomerDialog(customer);
				break;
			case 'delete':
				deleteCustomer(customer);
				break;
			case 'add-location':
				openLocationDialog(customer);
				break;
		}
	} else {
		// Location actions
		const location = item as Location & { _isSubLocation: boolean };
		switch (action) {
			case 'edit':
				openLocationDialog(null, location);
				break;
			case 'delete':
				deleteLocation(location);
				break;
		}
	}
};

const openCustomerDialog = (customer?: Customer) => {
	editingCustomer.value = customer ? { ...customer } : null;
	customerFormError.value = '';
	showCustomerDialog.value = true;
};

const closeCustomerDialog = () => {
	showCustomerDialog.value = false;
	editingCustomer.value = null;
	customerFormError.value = '';
};

const openLocationDialog = (customer?: Customer | null, location?: Location) => {
	selectedCustomerForLocation.value = customer || null;
	editingLocation.value = location ? { ...location } : null;
	locationFormError.value = '';
	showLocationDialog.value = true;
};

const closeLocationDialog = () => {
	showLocationDialog.value = false;
	editingLocation.value = null;
	selectedCustomerForLocation.value = null;
	locationFormError.value = '';
};

const saveCustomer = async () => {
	if (!editingCustomer.value) return;
	
	customerFormLoading.value = true;
	try {
		if (editingCustomer.value.customerId) {
			// Update existing customer
			const index = customers.value.findIndex(c => c.customerId === editingCustomer.value!.customerId);
			if (index > -1) {
				customers.value[index] = { ...editingCustomer.value };
			}
		} else {
			// Add new customer
			const newCustomer = {
				...editingCustomer.value,
				customerId: Date.now(), // Temporary ID generation
				locations: []
			};
			customers.value.push(newCustomer);
		}
		closeCustomerDialog();
	} catch (error) {
		customerFormError.value = 'Error saving customer';
		console.error('Error saving customer:', error);
	} finally {
		customerFormLoading.value = false;
	}
};

const saveLocation = async () => {
	if (!editingLocation.value || !selectedCustomerForLocation.value) return;
	
	locationFormLoading.value = true;
	try {
		const customer = customers.value.find(c => c.customerId === selectedCustomerForLocation.value!.customerId);
		if (customer) {
			if (editingLocation.value.locationId) {
				// Update existing location
				const index = customer.locations.findIndex(l => l.locationId === editingLocation.value!.locationId);
				if (index > -1) {
					customer.locations[index] = { ...editingLocation.value };
				}
			} else {
				// Add new location
				const newLocation = {
					...editingLocation.value,
					locationId: Date.now(), // Temporary ID generation
					customerId: customer.customerId
				};
				customer.locations.push(newLocation);
			}
		}
		closeLocationDialog();
	} catch (error) {
		locationFormError.value = 'Error saving location';
		console.error('Error saving location:', error);
	} finally {
		locationFormLoading.value = false;
	}
};

const deleteCustomer = async (customer: Customer) => {
	if (confirm(`Are you sure you want to delete customer "${customer.businessName}"?`)) {
		const index = customers.value.findIndex(c => c.customerId === customer.customerId);
		if (index > -1) {
			customers.value.splice(index, 1);
		}
	}
};

const deleteLocation = async (location: Location) => {
	if (confirm(`Are you sure you want to delete this location?`)) {
		const customer = customers.value.find(c => c.customerId === location.customerId);
		if (customer && customer.locations) {
			const index = customer.locations.findIndex(l => l.locationId === location.locationId);
			if (index > -1) {
				customer.locations.splice(index, 1);
			}
		}
	}
};

const handleCustomerValidation = (isValid: boolean) => {
	// Handle customer form validation
};

const handleLocationValidation = (isValid: boolean) => {
	// Handle location form validation
};

const handleLocationAdded = (location: Location) => {
	// Handle when a new location is added via LocationManager
	if (selectedCustomerForLocation.value) {
		const customer = customers.value.find(c => c.customerId === selectedCustomerForLocation.value!.customerId);
		if (customer) {
			customer.locations.push(location);
		}
	}
};

// Table headers for customers
const customerHeaders = [
	{ title: 'Business Name', key: 'businessName', sortable: false, width: '200px' },
	{ title: 'Display Name', key: 'displayName', sortable: false, width: '150px' },
	{ title: 'Customer Code', key: 'customerCode', sortable: false, width: '120px' },
	{ title: 'Status', key: 'status', sortable: false, width: '100px' },
	{ title: 'Region', key: 'region', sortable: false, width: '120px' },
	{ title: 'Customer Type', key: 'customerType', sortable: false, width: '120px' },
	{ title: 'Website', key: 'website', sortable: false, width: '150px' },
	{ title: 'Relationship', key: 'relationshipStatus', sortable: false, width: '120px' },
	{ title: 'Actions', key: 'actions', sortable: false, width: '100px' }
];

// Table headers for locations
const locationHeaders = [
	{ title: 'Location Name', key: 'displayName', sortable: false, width: '200px' },
	{ title: 'Type', key: 'locationType', sortable: false, width: '120px' },
	{ title: 'Primary', key: 'isPrimary', sortable: false, width: '100px' },
	{ title: 'Address', key: 'address', sortable: false, width: '200px' },
	{ title: 'City', key: 'city', sortable: false, width: '120px' },
	{ title: 'Province', key: 'state', sortable: false, width: '100px' },
	{ title: 'Postal Code', key: 'postalCode', sortable: false, width: '120px' },
	{ title: 'Country', key: 'country', sortable: false, width: '80px' },
	{ title: 'Actions', key: 'actions', sortable: false, width: '100px' }
];

// Customer and Location types based on API
type Customer = ApiCustomer & { _isExpanded?: boolean };
type Location = ApiLocation;

// Form data interfaces
interface CustomerFormData {
	businessName: string;
	displayName: string;
	legalName: string;
	customerCode: string;
	status: string;
	sfOpportunityId: string;
	relationshipStatus: string;
	customerType: string;
	website: string;
	region: string;
	isGlobalAgreement: 'Y' | 'N';
	isSubsidiary: 'Y' | 'N';
}

interface LocationFormData {
	displayName: string;
	locationType: string;
	isPrimary: 'Y' | 'N';
	addressLine1: string;
	addressLine2: string | null;
	addressLine3: string | null;
	city: string;
	state: string;
	country: string;
	postalCode: string;
	province: string;
}

// Form data for add/edit operations
const customerFormData = ref<CustomerFormData>({
	businessName: '',
	displayName: '',
	legalName: '',
	customerCode: '',
	status: 'Active',
	sfOpportunityId: '',
	relationshipStatus: '',
	customerType: '',
	website: '',
	region: '',
	isGlobalAgreement: 'N',
	isSubsidiary: 'N'
});

const locationFormData = ref<LocationFormData>({
	displayName: '',
	locationType: 'Office',
	isPrimary: 'N',
	addressLine1: '',
	addressLine2: null,
	addressLine3: null,
	city: '',
	state: '',
	country: 'CA',
	postalCode: '',
	province: ''
});

// Form validation errors
const customerFormErrors = ref<Record<string, string>>({});
const locationFormErrors = ref<Record<string, string>>({});

// Load customers on component mount
const loadCustomers = async () => {
	loading.value = true;
	try {
		const apiCustomers = await getCustomers();
		customers.value = apiCustomers.map(customer => ({
			...customer,
			_isExpanded: false
		}));
	} catch (error) {
		console.error('Error loading customers:', error);
	} finally {
		loading.value = false;
	}
};

onMounted(() => {
	loadCustomers();
});
</script>

<style scoped>
.customer-management-page {
	padding: 20px;
}

.bg-grey-lighten-5 {
	background-color: rgb(var(--v-theme-surface-variant)) !important;
}

.text-grey-darken-2 {
	color: rgb(var(--v-theme-on-surface-variant)) !important;
}
</style>
