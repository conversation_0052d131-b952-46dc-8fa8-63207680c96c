<template>
  <v-dialog v-model="isDialogVisible" max-width="400">
    <v-card>
      <v-card-title class="text-h5">{{ dialogTitle }}</v-card-title>
      <v-card-text>{{ dialogMessage }}</v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn @click="confirmAction">{{ confirmText }}</v-btn>
        <v-btn @click="cancelAction">{{ cancelText }}</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup lang="ts">
import { ref, watch, defineProps, computed } from 'vue';
import { useDialogStore } from '@/stores/DialogStore';

const dialogStore = useDialogStore();

const props = defineProps({
  title: {
    type: String,
    default: '',
  },
  message: {
    type: String,
    default: '',
  },
  confirmText: {
    type: String,
    default: 'Yes',
  },
  cancelText: {
    type: String,
    default: 'No',
  },
  isVisible: {
    type: Boolean,
    required: true,
  },
});

const emit = defineEmits(['confirm', 'cancel', 'update:isVisible']);

const isDialogVisible = ref(props.isVisible);

// Use either the prop values or the store values
const dialogTitle = computed(() => dialogStore.dialogTitle || props.title || 'Confirmation');
const dialogMessage = computed(() => dialogStore.dialogMessage || props.message || 'Are you sure you want to proceed?');
const confirmText = computed(() => dialogStore.confirmButtonText || props.confirmText || 'Yes');
const cancelText = computed(() => dialogStore.cancelButtonText || props.cancelText || 'No');

const confirmAction = () => {
  // Execute the callback from the store first
  dialogStore.executeConfirmCallback();
  // Then emit the confirm event
  emit('confirm');
  isDialogVisible.value = false;
  emit('update:isVisible', false);
};

const cancelAction = () => {
  emit('cancel');
  isDialogVisible.value = false;
  emit('update:isVisible', false);
};

watch(() => props.isVisible, (newVal) => {
  isDialogVisible.value = newVal;
});
</script>

<style scoped>
/* Add any custom styles if necessary */
</style>
