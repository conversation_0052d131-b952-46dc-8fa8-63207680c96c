<template>
  <v-autocomplete
    :model-value="modelValue"
    @update:model-value="updateValue"
    :label="label"
    :items="employees"
    item-title="displayName"
    item-value="id"
    return-object
    no-filter
    :loading="loading"
    :error-messages="errorMessages"
    clearable
    class="mt-4"
    v-model:search="localSearch"
    @update:search="onSearch"
    open-on-clear
    open-on-click
    :disabled="disabled"
  >
    <template v-slot:append-inner>
      <v-progress-circular v-if="loading" indeterminate size="20" />
    </template>

    <template v-slot:item="{ props, item }">
      <v-list-item v-bind="props">
        <v-list-item-title>
          {{ item?.raw?.displayName || '' }} | {{ item?.raw?.mail || '' }}
        </v-list-item-title>
      </v-list-item>
    </template>
  </v-autocomplete>
</template>

<script setup>
import { ref, watch } from 'vue'
const props = defineProps({
  modelValue: Object, // Required for v-model
  label: String,
  employees: Array,
  loading: Boolean,
  errorMessages: [String, Array],
  search: { type: String, default: '' },
  disabled: Bo<PERSON><PERSON>,
})

const emit = defineEmits(['update:modelValue', 'update:search'])

const localSearch = ref(props.search)

watch(() => props.search, (val) => {
  localSearch.value = val || ''
})

const updateValue = (val) => {
  emit('update:modelValue', val)
}

const onSearch = (val) => {
  emit('update:search', val)
}
</script>
