<template>
  <service-approval-table
    :term-length="termLength"
    :service-value-pack="serviceValuePack"
    :service-levels="serviceLevels"
    :sections-data="tableSections"
    :customer-name="customerName"
    @update:term-length="termLength = $event"
    @update:service-value-pack="serviceValuePack = $event"
    @submit="handleSubmit"
  />
</template>

<script setup lang="ts">
import { ref, onMounted, watch } from 'vue';
import ServiceApprovalTable from '@/components/service/ServiceApprovalTable.vue';
import { getServiceApprovalOptions, postServiceApprovalOptions, type DsdRequestServiceApprovalOptionInput } from '@/lib/api';
import { getLov, LovCategories, type LovItem } from '@/services/lovService';
import { useAppStore } from '@/stores/AppStore';
import { useSnackbarStore } from '@/stores/SnackbarStore';

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

// Sample data - replace with actual data fetching
const termLength = ref("5");
const serviceValuePack = ref<string>('');
const serviceLevels = ref<{ label: string; value: string }[]>([]);
const customerName = ref<string>('');

const tableSections = ref<any[]>([]);
  const appStore = useAppStore();
  const snackbarStore = useSnackbarStore();
onMounted(async () => {
  // Fetch dropdown values for Service Value Pack
  try {
    const lovData = await getLov(LovCategories.SERVICE_VALUE_PACK, false);
    serviceLevels.value = lovData.map((item) => ({ label: item.value, value: item.lookupCode }));
  } catch (err) {
    console.error('Failed to load Service Value Pack LOV:', err);
  }

  try {
    const response = await getServiceApprovalOptions(props.id);
    const apiResponse = response.data;

    // Map new API response
    // Capture service pack id and customer name for header and Excel filename
    if (apiResponse?.servicePackId !== undefined && apiResponse?.servicePackId !== null) {
      serviceValuePack.value = String(apiResponse.servicePackId);
    }
    customerName.value = apiResponse?.customer?.displayName || apiResponse?.customer?.businessName || '';

    const apiData = (apiResponse?.serviceApprovalOptions ?? []);

    // Group API data by itemId to handle multiple term options for the same product
    const itemsById = apiData.reduce((acc: Record<string, any[]>, item: any) => {
      if (!acc[item.itemId]) {
        acc[item.itemId] = [];
      }
      acc[item.itemId].push(item);
      return acc;
    }, {} as Record<string, any[]>);

    const colorItems = Object.values(itemsById).map((itemGroup: any) => {
      const firstItem = itemGroup[0]; // Product details are the same across the group

      const serviceOptions = itemGroup.map((option: any) => ({
        termLength: `${option.fixedTermInMonth} Months`,
        Requested: {
          "B&W": option.blackAndWhite,
          "Colour": option.colour,
          "Extra Long (IPRC only)": option.iprc,
          "Base $Amt": option.minimumBaseAmount,
          "Base Volume": option.minimumBaseVolume,
        },
        // Do NOT pre-populate approved values
        Approved: {
          "B&W": null,
          "Colour": null,
          "Extra Long (IPRC only)": null,
          "Base $Amt": null,
          "Base Volume": null,
        },
        serviceApprovalId: option.serviceApprovalId,
        serviceFormId: apiResponse?.serviceFormId,
      }));

      return {
        "Model": firstItem.displayName,
        "Qty": firstItem.dsdQuantity, // Or dealerQuantity?
        "Estimated AMV / Unit": firstItem.amvUnit,
        "Colour %": firstItem.colourPercentage,
        "Oversize %": firstItem.oversizePercentage,
        "Inclusive Plan Yes/No": firstItem.cpcIncludesAccessory,
        serviceOptions: serviceOptions,
      };
    });

    tableSections.value = [
      {
        section_name: "B&W",
        data_rows: [] // Assuming B&W items might be separate, or filter from main list
      },
      {
        section_name: "Color",
        data_rows: colorItems
      }
    ];
    appStore.stopPageLoader();
  } catch (error) {
    console.error('Failed to load service approval options:', error);
    appStore.stopPageLoader();
  }
});

// Helper to sanitize numeric fields coming from table (replace '-' or empty with null and ensure number)
const sanitize = (val: any) => {
  if (val === '-' || val === '' || val === undefined || val === null) return null;
  return typeof val === 'string' ? parseFloat(val) : val;
};

const handleSubmit = async (approvedData: any) => {
  try {
    // The approvedData from the event is expected to be the list of items.
    // We need to transform it to the POST API payload format matching DsdRequestServiceApprovalOptionInupt structure.
    const approvalInputs = approvedData.flatMap((section: any) => 
        section.data_rows.flatMap((row: any) => 
            row.serviceOptions.map((option: any) => ({
                serviceApprovalId: option.serviceApprovalId,
                serviceFormId: option.serviceFormId,
                blackAndWhite: sanitize(option.Approved['B&W']),
                colour: sanitize(option.Approved['Colour']),
                iprc: sanitize(option.Approved['Extra Long (IPRC only)']),
                minimumBaseAmount: sanitize(option.Approved['Base $Amt']),
                minimumBaseVolume: sanitize(option.Approved['Base Volume']),
                userRemark: "Updated from approval screen" // Or get from a field
            }))
        )
    );

    // Extract serviceFormId and servicePackId from the first approval input
    const firstApprovalInput = approvalInputs[0];
    if (!firstApprovalInput) {
      throw new Error('No approval data to submit');
    }

    const payload: DsdRequestServiceApprovalOptionInput = {
      serviceFormId: firstApprovalInput.serviceFormId,
      servicePackId: parseInt(serviceValuePack.value) || null,
      approvalInputs: approvalInputs
    };

    await postServiceApprovalOptions(payload);
    snackbarStore.show({
      text: 'Approval submitted successfully',
      color: 'success',
      timeout: 3000,
    });
    // Optionally, navigate away or show a success message
  } catch (error) {
    console.error('Error submitting approval:', error);
    appStore.stopPageLoader();
    snackbarStore.show({
      text: 'Error submitting approval',
      color: 'error',
      timeout: 3000,
    });
  }
};
</script>

<style scoped>
/* Add any page-specific styles here */
</style>
