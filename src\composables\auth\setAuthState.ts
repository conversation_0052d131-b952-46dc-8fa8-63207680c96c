/**
 * @file Determines if a user is logged in and sets the applications user authenticate state accordingly.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <PERSON><PERSON> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { MSGraphUser } from '@/lib/common/types';

import { useUserStore } from '@/stores/UserStore';
import { useAppStore } from '@/stores/AppStore';
import { CanonAuth } from '@/lib/canonAuth';
import { getMe, getUserPhotoURL, getUserReportes } from '@/lib/msGraph';
import { CN_Action, CanonAuthState, MSGraphUserPhotoSize } from '@/lib/common/types';
import actionPermissions from '@/composables/auth/actionPermission';
import { postLogin, updateUserProfile } from '@/lib/api';
import getAccessToken from './getAccessToken';
import { useInternalUserStore } from '@/stores/InternalUserStore';

/**
 * ----
 * Export
 * ----
*/

/**
 * Processes a authentication failure.
 *
 * @param {CanonAuthState} authState Helps determine the error that should be thrown.
 * @return {*}  {Promise <boolean>}
 */
export const authFail = async ( authState : CanonAuthState ) : Promise <boolean> =>
{
    // Update session.
    CanonAuth.authState = authState;
    
    // Add stores.
    const userStore = useUserStore();
    
    // Completely clean the auth session.
    await CanonAuth.cleanSession();

    // Completely clean user store.
    userStore.clearUser();

    return false;
}

export default async () : Promise <boolean> =>
{
    try
    {
        // Validate if user has been authenticated by Microsoft (MSAL).
        if ( CanonAuth.isAuthenticated() === false )
        {
            return authFail( CanonAuthState.UNKNOWN_ERROR );
        }

        // Validate if user has correct roles.
        if ( !CanonAuth.getRoles() === null || !actionPermissions( CN_Action.PA_LOGIN ) )
        {
            return authFail( CanonAuthState.NO_ACCESS );
        }

        // Gets ID and Access Tokens with default (login) scopes.
        const tokenResult = await CanonAuth.getToken( undefined, true );

        // Validate if valid ID and Access Token are available.
        if ( !tokenResult || !tokenResult.idToken || !tokenResult.accessToken)
        {
            return authFail( CanonAuthState.SESSION_EXPIRED );
        }

        // !! TODO !! - Must create Login API before enabling this line.
        // Send login request to local application.
        const token=await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE],false );
        const additionalHeaders = {
            'Authorization': `Bearer ${token}`
        };
        const loginResponse = await postLogin( { idToken: tokenResult.idToken },additionalHeaders );

        // !! TODO !! - Remove this after Login API is created - TESTING ONLY!
        // const loginResponse = 
        // {
        //     data:
        //     {
        //         language: 'en-US',
        //         theme: 'light'
        //     }
        // }

        // If application login was successful, carry out the rest of the user init process.
        if ( loginResponse )
        {
            // Add stores.
            const userStore = useUserStore();
            const appStore = useAppStore();
            const userInternalStore = useInternalUserStore();

            // Get user details from MS Graph.
            const userDetails : MSGraphUser = await getMe( tokenResult.accessToken );
            const isUserManager  = await getUserReportes( tokenResult.accessToken );
            // console.log(isUserManager)
            // Get users photo.
            const userPhoto = await getUserPhotoURL( tokenResult.accessToken, null, MSGraphUserPhotoSize.SIZE_2 );

            const updatedUserDetails=await updateUserProfile(userDetails.microsoftID,{approverMicrosoftAccountId:userDetails.manager?.microsoftID,isApprovalFlag:isUserManager})
            // Set user data to store.
            userStore.setUser( userDetails, userPhoto || undefined );
            userInternalStore.setInternalUser({...loginResponse.data,...updatedUserDetails.data})

            // Set app settings based on profile values.
            if ( loginResponse.data.language &&  loginResponse?.data?.theme)
            {
                appStore.setLanguage( loginResponse?.data?.language, false );
                appStore.setTheme( loginResponse?.data?.theme, false );
            }

            // Update session.
            CanonAuth.authState = CanonAuthState.IN_SESSION;

            return true;
        }
    }
    catch( e : any )
    {
        console.log(e);
        // Check if account is disabled by app admins.
        if ( e?.response?.status === 401 )
        {
            // Update session.
            CanonAuth.authState = CanonAuthState.DISABLED;
        }
    }

    return authFail( CanonAuthState.UNKNOWN_ERROR );
};