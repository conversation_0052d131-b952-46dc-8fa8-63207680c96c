<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: String, // Corresponds to formData.competitive_current_usage_info.details
    required: true,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const requiredRule = (value: any) => !!value || 'This field is required.';

const localDetails = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

</script>

<template>
  <v-card class="mb-4">
    <v-card-title>Competitive & Current Usage Information <span style="color:red">*</span></v-card-title>
    <v-alert density="compact" type="info" variant="tonal" style="margin: 0px 16px;">
      Please Provide Detailed Information Regarding The Competition's CPC & Contract Terms as well as information regarding the customer's current printing behaviours
    </v-alert>
    <v-alert density="compact" type="info" variant="tonal" style="margin: 5px 16px 0px;">
      Please provide Serial Numbers when replacing Canon Machine
    </v-alert>
    <v-card-text>
      <v-textarea
        v-model="localDetails"
        label=""
        rows="5"
        auto-grow
        variant="outlined"
        :rules="[requiredRule]"
         :maxlength="1000"
        counter
        persistent-counter
      />
    </v-card-text>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
</style>
