<template>
  <v-container>
    <v-card class="mx-auto" max-width="1000">
      <v-card-title class="headline text-center">
        CMAC Extension Request Form
      </v-card-title>
      <v-card-text>
        <v-form ref="form" v-model="valid">
          <!-- Customer Information Section -->
          <v-row>
            <v-col cols="4" class="font-weight-bold">Customer Name</v-col>
            <v-col cols="8"><v-text-field v-model="formData.customerName" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">CMAC Number</v-col>
            <v-col cols="8"><v-text-field v-model="formData.cmacNumber" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">MSA (YES/NO)</v-col>
            <v-col cols="8">
              <v-select v-model="formData.msa" :items="['YES', 'NO']" dense outlined></v-select>
            </v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Length of Extension</v-col>
            <v-col cols="8"><v-text-field v-model="formData.extensionLength" dense outlined></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Models and Quantities</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="formData.modelsAndQuantities" dense outlined rows="3"></v-textarea></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Size of Opportunity ($MSRP)</v-col>
            <v-col cols="8"><v-text-field v-model="formData.opportunitySize" dense outlined prefix="$"></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Extension Justification</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="formData.extensionJustification" dense outlined rows="5"></v-textarea></v-col>
          </v-row>

          <!-- Price Desk Use ONLY Section -->
          <v-divider class="my-6"></v-divider>
          <h2 class="subtitle-1 font-weight-bold black white--text pa-2">Price Desk Use ONLY</h2>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Revenue TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.revenueTd" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Target TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.targetTd" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Revenue vs. Target</v-col>
            <v-col cols="8"><v-text-field v-model="revenueVsTarget" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">Number of Extensions TD</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.numberOfExtensions" dense outlined readonly></v-text-field></v-col>
          </v-row>
           <v-row>
            <v-col cols="4" class="font-weight-bold">Support Average</v-col>
            <v-col cols="8"><v-text-field v-model="priceDesk.supportAverage" dense outlined readonly></v-text-field></v-col>
          </v-row>
          <v-row>
            <v-col cols="4" class="font-weight-bold">PD Justification</v-col>
            <v-col cols="8"><v-textarea :maxlength="500" v-model="priceDesk.pdJustification" dense outlined rows="5" readonly></v-textarea></v-col>
          </v-row>
        </v-form>
      </v-card-text>
    </v-card>
  </v-container>
</template>

<script>
export default {
  name: 'CmacExtensionRequestForm',
  data() {
    return {
      valid: true,
      formData: {
        customerName: '',
        cmacNumber: '',
        msa: 'NO',
        extensionLength: '',
        modelsAndQuantities: '',
        opportunitySize: null,
        extensionJustification: '',
      },
      priceDesk: {
        revenueTd: null,
        targetTd: null,
        numberOfExtensions: null,
        supportAverage: null,
        pdJustification: '',
      },
    };
  },
  computed: {
    revenueVsTarget() {
      const revenue = parseFloat(this.priceDesk.revenueTd);
      const target = parseFloat(this.priceDesk.targetTd);
      if (!isNaN(revenue) && !isNaN(target) && target !== 0) {
        return (revenue / target).toFixed(2);
      }
      if (target === 0 && revenue > 0) {
        return '#DIV/0!';
      }
      return '';
    },
  },
};
</script>

<style scoped>
.v-card-title {
  background-color: #f5f5f5;
  color: red;
  font-weight: bold;
}
.font-weight-bold {
    font-weight: bold;
}
.black.white--text {
    background-color: black !important;
    color: white !important;
}
.v-row {
    border-bottom: 1px solid #ccc;
    margin-bottom: 8px;
    padding-bottom: 8px;
}
.v-col {
    display: flex;
    align-items: center;
}
</style>
