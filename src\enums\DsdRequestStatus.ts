export enum DsdRequestStatus {
  DRAFT = 0,
  SERVICE_FORM_PENDING = 1,
  MANAGER_APPROVAL_PENDING = 2,
  RL_APPROVAL_PENDING = 3,
  PD_APPROVAL_PENDING = 4,
  SERVICE_APPROVAL_PENDING = 5,
  APPROVED = 6,
}

export function getDsdRequestStatusText(status: DsdRequestStatus | number): string {
  switch (status) {
    case DsdRequestStatus.DRAFT:
      return 'Draft';
    case DsdRequestStatus.SERVICE_FORM_PENDING:
      return 'Service Form Pending';
    case DsdRequestStatus.MANAGER_APPROVAL_PENDING:
      return 'Manager Approval Pending';
    case DsdRequestStatus.RL_APPROVAL_PENDING:
      return 'RL Approval Pending';
    case DsdRequestStatus.PD_APPROVAL_PENDING:
      return 'PD Approval Pending';
    case DsdRequestStatus.SERVICE_APPROVAL_PENDING:
      return 'Service Approval Pending';
    case DsdRequestStatus.APPROVED:
      return 'Approved';
    default:
      return 'Unknown';
  }
}
