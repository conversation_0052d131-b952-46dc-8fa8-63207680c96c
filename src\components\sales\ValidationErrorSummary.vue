<script setup lang="ts">
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

// Define props
const props = defineProps<{
  errors: Record<string, string>;
  isVisible: boolean;
}>();

// Define emits
const emit = defineEmits<{
  navigateToField: [fieldKey: string];
  close: [];
}>();

// Computed property to get error entries
const errorEntries = computed(() => {
  console.log(props.errors)
  return Object.entries(props.errors || {});
});

// Get user-friendly field names
const getFieldDisplayName = (fieldKey: string): string => {
  const fieldNameMap: Record<string, string> = {
    // Customer Details
    'isGlobalAgreement': 'Global Agreement',
    'customerRelationship': 'Customer Relationship',
    'pricingCategoryCode': 'Pricing Category Code',
    'implementation': 'Implementation',
    'softwareSalesStrategy': 'Software Sales Strategy',
    
    // Equipment/MFP Details
    'mfpIncumbents[0].incumbentPercent': 'MFP Incumbent Percent (Item 1)',
    'mfpIncumbents[1].incumbentPercent': 'MFP Incumbent Percent (Item 2)',
    'mfpIncumbents[2].incumbentPercent': 'MFP Incumbent Percent (Item 3)',
    
    // Service Form
    'serviceForm.servicePackId': 'Service Pack ID',
    'serviceForm.servicePackDescription': 'Service Pack Description',
    'serviceForm.currentUsageInformation': 'Current Usage Information',
    'serviceForm.currentEquipmentInfo': 'Current Equipment Info',
    'serviceForm.serviceBusinessCase': 'Service Business Case',
    'serviceForm.tonerType': 'Toner Type',
    'serviceForm.region': 'Region',
    'serviceForm.territory': 'Territory',
    'serviceForm.coterm': 'Co-term',
    'serviceForm.isDealerAcceptedSr': 'Dealer Accepted Service Rate',
  };
  
  return fieldNameMap[fieldKey] || fieldKey;
};

// Get tab name for field
const getTabForField = (fieldKey: string): string => {
  if (fieldKey.includes('serviceForm.')) return 'Service Request';
  if (fieldKey.includes('mfpIncumbents') || ['Installation Date', 'Current Incumbent', 'Potential Unit Count'].includes(fieldKey)) return 'Strategy';
  if (["Network Scan","Rfp","Msa","Print Assessment","Sales Branch"].includes(fieldKey)) {
    return 'Customer Details';
  }
  if([ "Pricing Category Code",
  "Minimum Commitment Amount",
  "Foreign Buyout",
  "Supplier Type",
  "Implementation",
  "Proposal Date",
  "Payment Mode",'Payment Justification','Lease Term In Month','Contract Type','Billing Period'].includes(fieldKey)) {
    return 'Payment Details';
  }
  return 'Unknown';
};

// Handle field navigation
const handleFieldClick = (fieldKey: string) => {
  emit('navigateToField', fieldKey);
};

// Handle close
const handleClose = () => {
  emit('close');
};
</script>

<template>
  <v-alert
    v-if="isVisible && errorEntries.length > 0"
    type="error"
    variant="tonal"
    closable
    class="mb-4"
    @click:close="handleClose"
  >
    <template v-slot:title>
      <div class="d-flex align-center">
        <v-icon class="mr-2">error_outline</v-icon>
        <span>Form Validation Errors ({{ errorEntries.length }})</span>
      </div>
    </template>
    
    <div class="mt-3">
      <p class="text-body-2 mb-3">
        Please fix the following errors before submitting the form:
      </p>
      
      <v-row>
        <v-col
          v-for="([fieldKey, errorMessage], index) in errorEntries"
          :key="fieldKey"
          cols="12"
          md="6"
          class="py-1"
        >
          <v-list density="compact" class="bg-transparent">
            <v-list-item
              class="px-0 py-1"
            >
              <template v-slot:prepend>
                <v-icon size="small" color="error" class="mr-2">
                  {{ index + 1 }}
                </v-icon>
              </template>

              <v-list-item-title class="text-body-2">
                <span class="font-weight-medium text-error">
                  {{ getFieldDisplayName(fieldKey) }}
                </span>
                <span class="text-caption text-grey ml-2">
                  ({{ getTabForField(fieldKey) }})
                </span>
              </v-list-item-title>

              <v-list-item-subtitle class="text-body-2 mt-1">
                {{ errorMessage }}
              </v-list-item-subtitle>
            </v-list-item>
          </v-list>
        </v-col>
      </v-row>
      
      <v-divider class="my-3"></v-divider>
      
      <div class="d-flex justify-space-between align-center">
        <span class="text-caption text-grey">
          <!-- Click on any error to navigate to the field -->
        </span>
        <v-btn
          size="small"
          variant="outlined"
          color="error"
          @click="handleClose"
        >
          Dismiss
        </v-btn>
      </div>
    </div>
  </v-alert>
</template>

<style scoped>
.v-list-item:hover {
  background-color: rgba(var(--v-theme-error), 0.04);
}

.v-list-item {
  border-radius: 4px;
  margin-bottom: 4px;
}
</style>
