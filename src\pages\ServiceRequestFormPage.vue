<script setup lang="ts">
import { ref, reactive, defineProps, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import ServiceHeaderInfo from '@/components/service/ServiceHeaderInfo.vue';
import ServiceTonerPack from '@/components/service/ServiceTonerPack.vue';
import ServiceApprovalsLease from '@/components/service/ServiceApprovalsLease.vue';
import ServiceProductDetails from '@/components/service/ServiceProductDetails.vue';
import ServiceAccessoryList from '@/components/service/ServiceAccessoryList.vue';
import ServiceCompetitiveInfo from '@/components/service/ServiceCompetitiveInfo.vue';
import ServiceCurrentEquipment from '@/components/service/ServiceCurrentEquipment.vue';
import ServiceBusinessCase from '@/components/service/ServiceBusinessCase.vue';
import * as XLSX from 'xlsx';
// Import other service components here as they are created

const { t } = useI18n();

const props = defineProps({
  id: {
    type: String,
    required: false, // As the ID is optional in the route
    default: null,
  },
});

// Initialize formData based on the UI plan
const formData = reactive({
  service_request_form: {
    request_date: null,
    sales_representative_name: '',
    sales_manager_name: '',
    customer_business_name: '',
    customer_legal_name: '',
    address1: '',
    address2_3: '',
    city_province_postal: '',
  },
  toner_and_service_value_pack: {
    toner_in_out: null, // Default to null for placeholder
    service_value_pack: null, // Default to null for placeholder
  },
  approvals_details: {
    selected_region: null, // Changed from 'region' object
    dsd_territory: false,
    dealer_territory: false,
    dealer_accepted_service_rates: null, // 'Yes' or 'No'
    // coterm: null, // Removed
    purchase_lease_type: 'Lease', // Default
    length_of_lease_months: null,
    msrp_details: { percentage: null, amount: null },
  },
  product_service_details: {
    models_overview: [
      {
        id: 'prod_1',
        model: 'imageRUNNER ADVANCE DX C5850i',
        dsd_qty: 1,
        dealer_qty: 0,
        estimated_amv_unit: 15000,
        colour_percentage: 60,
        oversize_percentage: 10,
        fees_included_in_cpc: false,
        published_rates: { bw: null, colour: null, minimum_base_amt: null, minimum_base_volume: null },
        discounts: { bw: 0, colour: 0, minimum_base_amt: 0, minimum_base_volume: 0 },
        requested_rates: {
          option1: {
            fixed_service_term_length: 36,
            bw: null,
            colour: null,
            minimum_base_amt: null,
            minimum_base_volume: null
          }
        }
      },
      {
        id: 'prod_2',
        model: 'imageRUNNER ADVANCE DX C5860i',
        dsd_qty: 2,
        dealer_qty: 1,
        estimated_amv_unit: 18000,
        colour_percentage: 70,
        oversize_percentage: 5,
        fees_included_in_cpc: true,
        published_rates: { bw: null, colour: null, minimum_base_amt: null, minimum_base_volume: null },
        discounts: { bw: 10, colour: 10, minimum_base_amt: 0, minimum_base_volume: 0 },
        requested_rates: {
          option1: {
            fixed_service_term_length: 60,
            bw: null,
            colour: null,
            minimum_base_amt: null,
            minimum_base_volume: null
          }
        }
      }
    ], // Array of product objects, managed by ServiceProductDetails
    accessories_included: [], // For the separate accessories table later
  },
  competitive_current_usage_info: {
    details: '',
  },
  current_equipment_details: {
    equipment_list: '',
  },
  service_business_case: {
    justification: 'Entera is mainly a Sharp customer with 3 Sharp units on the floor. They are currently paying a higher CPC for their Sharp units. They have agreed to move to Canon if we can provide them with a competitive CPC. The customer is also looking for a reliable service provider and Canon has a good reputation in the market. We are requesting a 10% discount on the service rates to win this deal. Potential penalties for not meeting service level agreements are 5% of the monthly service charge.', // Default text as per UI plan
  },
});

const handleFormSubmit = () => {
  console.log('Submitting Service Request Form:', formData);
  // API submission logic will go here
};

const cancelForm = () => {
  // Logic to navigate away or reset form
  console.log('Form cancelled');
};

const exportData = () => {
  const wb = XLSX.utils.book_new();

  // --- 1. Summary Sheet --- //
  const summaryData = [];
  const addSectionToSummary = (sectionData, sectionTitle) => {
    summaryData.push({ Section: `--- ${sectionTitle} ---`, Value: '' });
    const flattenObject = (obj, prefix = '') => {
      Object.keys(obj).forEach(key => {
        const pre = prefix ? prefix + ' ' : '';
        const value = obj[key];
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
          flattenObject(value, pre + key);
        } else if (!Array.isArray(value)) {
          summaryData.push({ Section: pre + key, Value: value });
        }
      });
    };
    flattenObject(sectionData, '');
    summaryData.push({ Section: '', Value: '' }); // Spacer
  };

  addSectionToSummary(formData.service_request_form, 'Header & Basic Request Information');
  addSectionToSummary(formData.toner_and_service_value_pack, 'Toner and Service Value Pack');
  addSectionToSummary(formData.approvals_details, 'Approvals and Lease Details');
  addSectionToSummary({ competitive_info: formData.competitive_current_usage_info.details }, 'Competitive & Current Usage Information');
  addSectionToSummary({ current_equipment: formData.current_equipment_details.equipment_list }, 'Current Equipment Details');
  addSectionToSummary({ business_case: formData.service_business_case.justification }, 'Service Business Case');

  const summaryWs = XLSX.utils.json_to_sheet(summaryData);
  XLSX.utils.book_append_sheet(wb, summaryWs, 'Summary');

  // --- 2. Product Details Sheet --- //
  if (formData.product_service_details.models_overview && formData.product_service_details.models_overview.length > 0) {
    const productData = formData.product_service_details.models_overview.map(p => ({
      'Model': p.model,
      'DSD Qty': p.dsd_qty,
      'Dealer Qty': p.dealer_qty,
      'Est. AMV/Unit': p.estimated_amv_unit,
      'Colour %': p.colour_percentage,
      'Oversize %': p.oversize_percentage,
      'Fees in CPC': p.fees_included_in_cpc ? 'Yes' : 'No',
      'Published Rate (B&W)': p.published_rates.bw,
      'Published Rate (Colour)': p.published_rates.colour,
      'Published Min. Base Amount': p.published_rates.minimum_base_amt,
      'Published Min. Base Volume': p.published_rates.minimum_base_volume,
      'Discount (B&W)': p.discounts.bw,
      'Discount (Colour)': p.discounts.colour,
      // Assuming one requested rate option for simplicity
      'Requested Term (months)': p.requested_rates.option1?.fixed_service_term_length,
      'Requested Rate (B&W)': p.requested_rates.option1?.bw,
      'Requested Rate (Colour)': p.requested_rates.option1?.colour,
      'Requested Min. Base Amount': p.requested_rates.option1?.minimum_base_amt,
      'Requested Min. Base Volume': p.requested_rates.option1?.minimum_base_volume,
    }));
    const productsWs = XLSX.utils.json_to_sheet(productData);
    XLSX.utils.book_append_sheet(wb, productsWs, 'Product Details');
  }

  // --- 3. Accessories Sheet --- //
  if (formData.product_service_details.accessories_included && formData.product_service_details.accessories_included.length > 0) {
    const accessoriesWs = XLSX.utils.json_to_sheet(formData.product_service_details.accessories_included);
    XLSX.utils.book_append_sheet(wb, accessoriesWs, 'Accessories');
  }

  // Export the workbook
  XLSX.writeFile(wb, 'Service-Request-Export.xlsx');
};

onMounted(() => {
  if (props.id) {
    console.log('ServiceRequestFormPage mounted with ID:', props.id);
    // TODO: Fetch existing service request data using props.id
    // and populate formData. For example:
    // fetchServiceRequestData(props.id).then(data => {
    //   Object.assign(formData, data);
    // });
  } else {
    console.log('ServiceRequestFormPage mounted for new request (no ID).');
  }
});

</script>

<template>
  <v-container fluid>
    <v-form @submit.prevent="handleFormSubmit">
      <v-row>
        <v-col cols="12">
          <h1>Service Request Form</h1> <!-- Placeholder Title -->
        </v-col>
      </v-row>

      <!-- Section 1: Header & Basic Request Information -->
      <ServiceHeaderInfo v-model="formData.service_request_form" />

      <!-- Section 2: Toner and Service Value Pack -->
      <ServiceTonerPack v-model="formData.toner_and_service_value_pack" />

      <!-- Section 3: Approvals and Lease Details -->
      <ServiceApprovalsLease v-model="formData.approvals_details" />

      <!-- Section 4: Product/Service Details -->
      <ServiceProductDetails 
        v-model="formData.product_service_details.models_overview"
        :selected-service-value-pack="formData.toner_and_service_value_pack.service_value_pack"
      />

      <!-- Section 4.1: Accessories Included in Deal (Part of Product/Service Details) -->
      <ServiceAccessoryList v-model="formData.product_service_details.accessories_included" />

      <!-- Section 5: Competitive & Current Usage Information -->
      <ServiceCompetitiveInfo v-model="formData.competitive_current_usage_info.details" />

      <!-- Section 6: Current Equipment Details -->
      <ServiceCurrentEquipment v-model="formData.current_equipment_details.equipment_list" />

      <!-- Section 7: Service Business Case -->
      <ServiceBusinessCase v-model="formData.service_business_case.justification" />


      <v-row>
        <v-col cols="12" class="d-flex justify-end mt-4">
          <v-btn color="secondary" variant="outlined" @click="exportData" class="mr-2">
            Export
          </v-btn>
          <v-btn color="error" variant="outlined" @click="cancelForm" class="mr-2">
            Cancel
          </v-btn>
          <v-btn color="primary" type="submit">
            Submit
          </v-btn>
        </v-col>
      </v-row>
    </v-form>
  </v-container>
</template>

<style scoped>
/* Add any page-specific styles here */
</style>
