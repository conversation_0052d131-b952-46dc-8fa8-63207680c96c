# CCI IT - Application Template

> **Important**: This branch implements the VueI18n library for English and French support.

The full documentation for this template is located at the links below:

- [Template Features](https://canonusa365.sharepoint.com/sites/cci_bu_it_dev/docs/SitePages/General/Template-Features.aspx)
- [Template Usage](https://canonusa365.sharepoint.com/sites/cci_bu_it_dev/docs/SitePages/General/Template-Usage.aspx)
- [Template User Properties](https://canonusa365.sharepoint.com/sites/cci_bu_it_dev/docs/SitePages/General/Template-User-Properties.aspx)
- [Template Deployment](https://canonusa365.sharepoint.com/sites/cci_bu_it_dev/docs/SitePages/General/Template-Deployment.aspx)

## App Launch Order of Operations

The steps below outline the logic of the application when it is initially loaded in the browser.

1. Application entry point: `/src/main.ts`
   1. Initiates CanonAuth by running constructor and `init()` in `/src/lib/canonAuth/CanonAuth.ts`
      1. Checks to see if there was a MSAL redirect triggered for login `this.client.handleRedirectPromise()`
         1. If Yes: Sets the current user from MSAL cache
         1. If No: Looks to see if an existing user exists in MSAL cache
            1. If Yes: Sets the current user from MSAL cache
            2. If No: Does nothing (no login/user)
            3. If Error: Force cleans MSAL cache.
            4. Auth State set to `READY`
         2. If Error: Auth State set to `UNKNOWN_ERROR`
   1. Initiates Vue object
   2. Initiates Pinia object
   3. Initiates Router object
   4. Initiates Vuetify
   5. Mounts Vue to DOM
1. Vue application is loaded via `/src/App.vue`
   1. `appStore` is injected
   2. `messageStore` is injected
   3. Function is injected to trigger after component is mounted (`onMounted`).
   4. Template in `App.vue` is mounted which includes `app-snackbar`, `app-message`, `app-loader` and `router-view`
      1. `router-view` is not hydrated in DOM because conditions for its flags are not yet met
      2. Snackbar, Message and Loader are now usable.
   5. Vue Router is loaded and `CanonAuthGuard` is triggered for current path
      1. Checks if Canon Auth is in `READY` state
         1. If Yes: Triggers `setAuthState` composable to set the applications user login state
            1. Check to see if a user exists in MSAL cache
               1. If Yes: Get the users assigned roles & confirm they have login roles with `CanonAuth.hasLoginRole()`
                  1. If Yes: Try and get a refreshed access token for the user (gets from Microsoft - not cache)
                     1. If Success:
                        1. Use token to get user details from MS Graph
                        2. Use token to get user photo from MS Graph
                        3. Set the userStore data
                        4. Set the auth state to `IN_SESSION`
                     2. If Fail: Display login expired error and provide login option (which redirects)
                        1. If choose Login: Redirected to login
                        2. If choose Close:
                           1. Clean Canon Auth session
                           2. Set auth state to `NO_LOGIN` (via `CanonAuth.cleanSession()`)
                           3. Clean user store
                  2. If has no roles - or - no login roles: 
                     1. Display message to user about invalid roles
                     2. Clean Canon Auth session
                     3. Set auth state to `NO_LOGIN` (via `CanonAuth.cleanSession()`)
                     4. Clean user store
               2. If No:
                  1. Clean Canon Auth session
                  2. Set auth state to `NO_LOGIN` (via `CanonAuth.cleanSession()`)
                  3. Clean user store
            2. If Error:
               1. Display message to user about unknown error.
               2. Clean Canon Auth session
               3. Set auth state to `NO_LOGIN` (via `CanonAuth.cleanSession()`)
               4. Clean user store
      2. Checks if the route is public or secure
         1. Route is Public:
            1. Returns true (allows route load)
         2. Route is Secure:
            1. Checks to see if user is logged in to application
               1. Yes, Logged In: Check if user has required roles to access route
                  1. Yes, Has Roles: Return true (allow route to load)
                  2. No, Does Not Have Roles:
                     1. Check if route request was internal or external:
                        1. Internal: Return false and display access denied message. Leaves user on their previous route.
                        2. External: Return false and display access denied message. Message cannot be closed and app is disabled (blank).
               2. No, Not Logged In:
                  1. Triggers login process using `/src/composables/auth/userLoginProcess.ts` composable
                     1. See below section `Sign-in Order of Operations` for Login Process workflow
                     2. Check result from login process
                        1. If True: (This only happens if user was logged in via SSO - Not Redirect)
                           1. Check if route has role requirements and user has those roles
                              1. If Yes: Return true (allow route to load)
                              2. If No:
                                 1. Check if route request was internal or external:
                                    1. Internal: Return false and display access denied message. Leaves user on their previous route.
                                    2. External: Return false and display access denied message. Message cannot be closed and app is disabled (blank).
                        2. If False: Redirect route to home page (root) - This will re-run the Canon Auth Guard to a public page - WORKFLOW STOPS.
                        3. If Redirect: User would have been redirected and all further code ends - WORKFLOW STOPS.
                        4. If Error: Display an error and redirect route to home page (root) - This will re-run the Canon Auth Guard to a public page - WORKFLOW STOPS.

## Sign-in Order of Operations

The steps below outline the logic performed when a sign-in request is performed via the `/src/composables/auth/userLoginProcess.ts` composable. This is used for every sign-in button in the application.

   1. `appStore` is injected
   2. `messageStore` is injected
   3. `snackbarStore` is injected
   4. Triggers the auth login with `CanonAuth.doLogin()`
      1. Triggers the silent login attempt with `doLoginSSO()`
         1. If an existing MSAL cached account is detected, adds a hint to the SSO request
         2. Clean existing MSAL cache of any existing records
            1. Sets auth state to `NO_LOGIN`
         3. Check with Microsoft if an existing SSO session is active with `this.client.ssoSilent()`
            1. If Yes: Check if user has roles and has appropriate roles to access application
               1. If Yes:
                  1. Set the MSAL active account with `this.client.setActiveAccount()`
                  2. Set the Canon Auth account
                  3. Return `true` (Auth State will be whatever previous state was)
               2. If No:
                  1. Update auth state to `NO_ACCESS`
                  2. Clean MSAL user session cache
                  3. Return `false` (Auth State will be `NO_ACCESS`)
            2. If No: 
               1. Clean MSAL user session cache
               2. Return `false` (Auth State will be `NO_LOGIN`)
            3. If Error:
               1. Clean MSAL user session cache
               2. Check Error:
                  1. Microsoft error is that user has not been provided a role, update auth state to `NO_ACCESS`
                  2. Return `false` (Auth State will be `NO_ACCESS`)
      2. If SSO login Successful (true):
         1. Return true (Auth State will be whatever previous state was)
      3. If SSO login Failed (false):
         1. Check auth state value:
            1. If auth state `NO_ACCESS` --> Return false - No further login attempt will be made.
            2. If auth state is any other --> Trigger a redirect login with `doLoginRedirect()`. 
               1. Trigger a MSAL `loginRedirect()` function which should redirect to Microsoft for login
                  1. If Success (true):
                     1. All code beyond this should never run as the user is redirected to Microsoft and the app is closed.
                     2. Update auth state to `REDIRECTED`
                     3. Return true
                  2. If Failed (false): Return false (Auth State will be `NO_LOGIN`)
                  3. If Error (false):
                     1. Update auth state to `UNKNOWN_ERROR`
                     2. Return false
   5. Check `CanonAuth.doLogin()` result
      1. If Success (true):
         1. Check auth state value
            1. If auth state `REDIRECTED`:
               1. Update loading message to indicate redirecting
               2. Return false --> USER IS NOT LOGGED IN
            2. If auth state any other value:
               1. Trigger `/src/composables/auth/setAuthState.ts` to set the login state for the application
                  1. See above `App Launch Order of Operations` section for order of operations for `setAuthState` which also runs on app load.
                  2. Check `setAuthState` response
                     1. If Success (true):
                        1. Display snackbar success login message to user
                        2. Return true --> USER IS LOGGED IN
                     2. If Failed (false):
                        1. Display could not be logged in message.
                        2. Return false --> USER NOT LOGGED IN
      2. If Failed (false):
         1. Check auth state value
            1. If auth state `NO_ACCESS`
               1. Display error message about no access
               2. Stop the application loader message
               3. Return false --> USER IS NOT LOGGED IN
            2. If auth state `NO_LOGIN` (Only happens if redirect failed somehow - Critical error with app)
            3. If auth state `UNKNOWN_ERROR`
               1. Display unknown error message to user
               2. Return false --> USER IS NOT LOGGED IN
   6. Exits the `userLoginProcess` composable process with returned boolean value.

## Testing

Want to simulate a delay or what? Use this code in a `async` function.

```js
await new Promise(resolve => setTimeout(resolve, 5000));
```