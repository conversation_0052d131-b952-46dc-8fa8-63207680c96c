<script setup lang="ts">
	/**
	 * @file Home (welcome) page.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { onMounted } from 'vue';
    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
    import useUserLoginProcess from '@/composables/auth/userLoginProcess';
    import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
    const appStore = useAppStore();
    const userStore = useUserStore();

    // Add language support.
    const { t } = useI18n();

    // Register login
    const startLoginProcess = useUserLoginProcess;

    // Define login button click.
    const goLogin = async () =>
    {
        appStore.startLoader( 'Login', t( 'app.auth.loader.logging' ) );

        await startLoginProcess( t );

        appStore.stopLoader( 'Login' );
    }

    onMounted ( () =>
    {
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8 viewport-centre">
		<v-row v-if="userStore.authenticated" class="text-center">
			<v-col cols="12">
				<span class="text-h3">{{ t( 'page.home.user.header' ) }}</span>
				<br /><br />
                <span class="text-body-1">{{ t( 'page.home.user.desc' ) }}</span>
			</v-col>
		</v-row>

		<v-row v-else class="text-center pt-3">
			<v-col cols="12">
                <span class="text-h3">{{ t( 'page.home.anonymous.header' ) }}</span>
				<br /><br />
                <span class="text-body-1">{{ t( 'page.home.anonymous.desc' ) }}</span>
                <br /><br />
				<v-btn @click="goLogin" size="x-large" flat color="primary" prepend-icon="login">{{ t( 'page.home.anonymous.button.signin.label' ) }}</v-btn>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>