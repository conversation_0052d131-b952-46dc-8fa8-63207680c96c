/**
 * @file Primary configuration for the Vite compiler process.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

// -------
// Imports
// -------

import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import VueDevTools from 'vite-plugin-vue-devtools';
import vuetify, { transformAssetUrls } from 'vite-plugin-vuetify';
import fs from "fs";

// -------
// Exports
// -------

export default defineConfig
({
    build:
    {
        outDir: 'deployment/dist/',
        chunkSizeWarningLimit: 1600
    },
    base: '/channelsupport',
    envDir: 'env',
    plugins:
    [
        vue( { template: { transformAssetUrls } } ),
        VueDevTools(),
        vuetify
        ({
            autoImport: true,
            styles:
            {
                configFile: 'src/styles/settings.scss',
            }
        })
    ],
    resolve:
    {
        alias:
        {
            '@': fileURLToPath( new URL( './src', import.meta.url ) )
        },
        extensions:
        [
            '.js',
            '.json',
            '.jsx',
            '.mjs',
            '.ts',
            '.tsx',
            '.vue',
        ]
    },
    server:
    {
        https:
        {
            key: fs.readFileSync( __dirname + "/tls/localhost.key"),
            cert: fs.readFileSync( __dirname + "/tls/localhost.crt")
        }
    }
});
