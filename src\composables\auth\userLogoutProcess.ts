/**
 * @file Logout process for application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { CanonAuth } from '@/lib/canonAuth';
import router from '@/router';

/**
 * ----
 * Export
 * ----
*/

export default async () : Promise <boolean> =>
{
    // Perform logout tasks for authentication.
    await CanonAuth.doLogout();

    // Get the path to home.
    const homePath = router.resolve( { name: 'pageHome' } );

    // Reload window and redirect to home.
    window.history.pushState( 'home', '', homePath?.href ? homePath.href : '/' );
    document.location.reload();

    // This should never execute but exists for code safety.
    return false;
};