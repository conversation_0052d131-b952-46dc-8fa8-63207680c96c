<script setup lang="ts">
	/**
	 * @file Home (welcome) page.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { onMounted, ref } from 'vue';
    import { useAppStore } from '@/stores/AppStore';
    import { useUserStore } from '@/stores/UserStore';
    import getAccessToken from '@/composables/auth/getAccessToken';
    import { CanonAuth } from '@/lib/canonAuth';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
    const appStore = useAppStore();
    const userStore = useUserStore();

    // Init token variables.
    let token = ref();
    let tokenHeader = ref();
    let tokenDecodedPayload = ref();
    let tokenGraph = ref();
    let tokenGraphHeader = ref();
    let tokenGraphDecodedPayload = ref();

    const getStandardToken = async () =>
    {
        // Get a specific (App.General) scope access token.
        token.value = await getAccessToken( [import.meta.env.VITE_APP_API_SCOPE] );
        const { header, payload } = CanonAuth.decodeToken( token.value );
        tokenHeader.value = JSON.stringify( header, null, 2 );
        tokenDecodedPayload.value = JSON.stringify( payload, null, 2 );
    }

    const getGraphToken = async () =>
    {
        // Get a general MS Graph token.
        tokenGraph.value = await getAccessToken();
        const { header, payload } = CanonAuth.decodeToken( tokenGraph.value );
        tokenGraphHeader.value = JSON.stringify( header, null, 2 );
        tokenGraphDecodedPayload.value = JSON.stringify( payload, null, 2 );
    }

    getStandardToken();
    getGraphToken();

    onMounted ( () =>
    {
        appStore.stopPageLoader();
    });
</script>

<template>
	<v-container class="pt-8">
		<v-row v-if="userStore.authenticated" class="text-center">
			<v-col cols="12">
				<span class="text-h3">User Troublshooting</span>
			</v-col>
            <v-col cols="12">
				<span>This page is for troubleshooting purposes.</span>
			</v-col>
		</v-row>

        <br><br>
        <hr>
        <br><br>

		<v-row class="pt-3">
			<v-col cols="12">
                <p class="text-h4 text-center">Current User Data</p>

                <v-form>
                    <v-container>
                        <v-row v-if="userStore.photoURL">
                            <v-col cols="12" md="12" class="d-flex justify-center my-5">
                                <v-avatar rounded size="128" align="center">
                                    <v-img :src="userStore.photoURL"></v-img>
                                </v-avatar>
                            </v-col>
                        </v-row>
                        <v-row>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Microsoft ID" type="input" readonly :model-value="userStore.microsoftID" persistent-hint hint="Unique ID assigned from Azure AD."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Canon Employee ID" type="input" readonly :model-value="userStore.employeeID" persistent-hint hint="Unique employee ID assigned by HR."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Login Username" type="input" readonly :model-value="userStore.userPrincipalName" persistent-hint hint="Email address used to login to M365."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Email Address" type="input" readonly :model-value="userStore.emailAddress" persistent-hint hint="Primary Canon email address."></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6">
                                <v-text-field density="compact" variant="outlined" label="Given Name" type="input" readonly :model-value="userStore.givenName" persistent-hint hint="Also known as first name."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Surname" type="input" readonly :model-value="userStore.surName" persistent-hint hint="Also known as last name."></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6">
                                <v-text-field density="compact" variant="outlined" label="Display Name" type="input" readonly :model-value="userStore.displayName" persistent-hint hint="Full name displayed in M365 applications (preferred name)."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Job Title" type="input" readonly :model-value="userStore.jobTitle" persistent-hint hint="Current employee job title."></v-text-field>
                            </v-col>
                            <v-col cols="6" md="6">
                                <v-text-field density="compact" variant="outlined" label="Department" type="input" readonly :model-value="userStore.department" persistent-hint hint="Current employee department."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Company Name" type="input" readonly :model-value="userStore.companyName" persistent-hint hint="Current Canon company which employees the employee."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="userStore.officeStreet" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Address: Street" type="input" readonly :model-value="userStore.officeCity" persistent-hint hint="Street address of Canon Company the employee is based out of."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Address: State/Province" type="input" readonly :model-value="userStore.officeState" persistent-hint hint="State/province of Canon Company the employee is based out of."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Address: Postal/Zip Code" type="input" readonly :model-value="userStore.officePostalCode" persistent-hint hint="Postal/Zip code of Canon Company the employee is based out of."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Address: Country" type="input" readonly :model-value="userStore.officeCountry" persistent-hint hint="Country of Canon Company the employee is based out of."></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Mobile" type="input" readonly :model-value="userStore.phoneMobile" persistent-hint hint="Mobile phone for employee (usually just extension)"></v-text-field>
                            </v-col>
                            <v-col cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Fax" type="input" readonly :model-value="userStore.phoneFax" persistent-hint hint="Fax phone for employee (usually blank or main number)."></v-text-field>
                            </v-col>
                            <v-col v-if="userStore.businessPhones" v-for="(phone, index) in userStore.businessPhones" cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" :label="`Business Phone ${ index + 1 }`" type="input" readonly :model-value="phone" persistent-hint hint="Business phone value (could be many)."></v-text-field>
                            </v-col>
                            <v-col v-if="userStore.manager" cols="12" md="6">
                                <v-text-field density="compact" variant="outlined" label="Manager" type="input" readonly :model-value="userStore.manager.displayName" persistent-hint hint="Manager of employee. This is an full user object with all details available."></v-text-field>
                            </v-col>
                        </v-row>
                    </v-container>
                </v-form>
			</v-col>
		</v-row>

        <br><br>
        <hr>
        <br><br>

        <v-row class="pt-3">
			<v-col cols="12">
                <p class="text-h4 text-center">Access Token: Backend API</p>
                <br>

                <v-row v-if="token">
                    <v-col>
                        <v-textarea label="Access Token Value (Encoded)" :model-value="token" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>

                <v-row v-if="tokenHeader">
                    <v-col>
                        <v-textarea label="Header (Decoded)" :model-value="tokenHeader" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>

                <v-row v-if="tokenDecodedPayload">
                    <v-col>
                        <v-textarea label="Payload (Decoded)" :model-value="tokenDecodedPayload" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>
			</v-col>
		</v-row>

        <br><br>
        <hr>
        <br><br>

        <v-row class="pt-3">
			<v-col cols="12">
                <p class="text-h4 text-center">Access Token: MS Graph</p>
                <br>

                <v-row v-if="tokenGraph">
                    <v-col>
                        <v-textarea label="Access Token Value (Encoded)" :model-value="tokenGraph" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>

                <v-row v-if="tokenGraphHeader">
                    <v-col>
                        <v-textarea label="Header (Decoded)" :model-value="tokenGraphHeader" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>

                <v-row v-if="tokenGraphDecodedPayload">
                    <v-col>
                        <v-textarea label="Payload (Decoded)" :model-value="tokenGraphDecodedPayload" variant="filled" auto-grow density="default"></v-textarea>
                    </v-col>
                </v-row>
			</v-col>
		</v-row>
	</v-container>
</template>

<style lang="scss"></style>