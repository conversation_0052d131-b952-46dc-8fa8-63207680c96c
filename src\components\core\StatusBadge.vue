<template>
    <span :class="badgeClass">{{ statusDescription }}</span>
  </template>
  
  <script setup lang="ts">
  import { ApprovalStatus, ApprovalStatusDescription } from '@/lib/common/types';
import { computed, defineProps } from 'vue';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();
  
  // Props definition
  const props = defineProps<{
    status: number;
  }>();
  
  // Get the translated status description
  const statusDescription = computed(() => {
    const descriptions=t( `status.${ApprovalStatusDescription[props.status as unknown as ApprovalStatus]}` ) || '';;
    return descriptions;
  });
  
  // Define a computed property for the badge class based on the status
  const badgeClass = computed(() => {
    switch (props.status) {
      case ApprovalStatus.STATUS_DRAFT:
        return 'badge-default';
        case ApprovalStatus.STATUS_MANAGER_APPROVAL_PENDING:
          return 'badge-panding';
      case ApprovalStatus.STATUS_RL_APPROVAL_PENDING:
        return 'badge-approved';
    case ApprovalStatus.STATUS_PD_APPROVAL_PENDING:
    return 'badge-rejected';
      default:
        return 'badge-default';
    }
  });
  </script>
  
  <style scoped>
  .badge-rejected {
    color: white;
    background-color: red;
  }
  
  .badge-approved {
    color: white;
    background-color: green;
  }
  
  .badge-panding {
    color: black;
    background-color: orange;
  }

  .badge-completed {
    color: white;
    background-color: gray;
  }
  

  .badge-default {
    color: white;
    background-color: gray;
  }
  
  span {
    padding: 0.25em 0.5em;
    border-radius: 4px;
    font-size: 0.875rem;
    font-weight: 500;
  }
  </style>
  