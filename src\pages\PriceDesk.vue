<script setup lang="ts">
/**
 * @file Price Desk page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { getPriceDeskList, type PriceDeskRequest } from '@/services/salesRequestService';
import DataTable from '@/components/common/DataTable.vue';
import StatusBadge from '@/components/core/StatusBadge.vue';
import { useAppStore } from '@/stores/AppStore';
import { useRouter } from 'vue-router';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();
const router = useRouter();

// Table data
const loading = ref(false);
const headers = computed(() => [
    { title: t('page.price_desk.table.header.request_number'), key: 'requestNumber', sortable: true },
    { title: t('page.price_desk.table.header.customer'), key: 'customer.businessName', sortable: true },
    { title: t('page.price_desk.table.header.created_by'), key: 'createdByName', sortable: true },
    { title: t('page.price_desk.table.header.last_updated'), key: 'updatedAt', sortable: true },
    { title: t('page.price_desk.table.header.status'), key: 'dsdRequestStatus', sortable: true },
    { title: t('common.actions'), key: 'actions', sortable: false }
]);

const items = ref<PriceDeskRequest[]>([]);

// Handle table actions
const handleAction = ({ action, item }: { action: string; item: any }) => {
    if (action === 'view') {
    router.push({ name: 'pageProfitLossCalculator', params: { id: item.requestId } });

        // Handle edit action
        console.log('Edit price:', item);
    } else if (action === 'approve') {
        // Handle approve action
        console.log('Approve price:', item);
    }
};

// Load data
const fetchData = async () => {
  loading.value = true;
  try {
    items.value = await getPriceDeskList();
  } catch (error) {
    console.error('Failed to fetch price desk list:', error);
    // Optionally, show an error message to the user
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
    fetchData();
    // Stop page loader when component is mounted
    appStore.stopPageLoader();
});
</script>

<template>
    <div class="pa-4">
                <h1>{{ t('page.price_desk.title') }}</h1>
        
        <!-- <v-card class="mt-4"> -->
        
            
            <v-card-text>
                <DataTable
                    :headers="headers"
                    :items="items"
                    :loading="loading"
                    @action="handleAction"
                >
                    <template v-slot:item.updatedAt="{ item }">
                        {{ new Date(item.updatedAt).toLocaleDateString() }}
                    </template>

                    <template v-slot:item.dsdRequestStatus="{ item }">
                        <StatusBadge :status="item.dsdRequestStatus || 0" />
                    </template>

                    <template v-slot:item.actions="{ item }">
                        <v-menu>
                            <template v-slot:activator="{ props }">
                                <v-btn
                                    icon="more_vert"
                                    variant="text"
                                    size="small"
                                    v-bind="props"
                                ></v-btn>
                            </template>
                            <v-list>
                                <v-list-item
                                    prepend-icon="visibility"
                                    :title="'View Profit & Loss'"
                                    @click="handleAction({ action: 'view', item })"
                                ></v-list-item>
                                <v-list-item
                                    prepend-icon="edit"
                                    :title="t('actions.edit')"
                                    @click="handleAction({ action: 'edit', item })"
                                ></v-list-item>
                            </v-list>
                        </v-menu>
                    </template>
                </DataTable>
            </v-card-text>
        <!-- </v-card> -->
    </div>
</template>
