<template>
  <v-container fluid>
    <ServiceApprovalHeader
      :term-length="termLength"
      :service-value-pack="serviceValuePack"
      :service-levels="serviceLevels"
      @update:termLength="(v) => $emit('update:term-length', v)"
      @update:serviceValuePack="(v) => $emit('update:service-value-pack', v)"
    />

    <div v-for="(section, sectionIndex) in tableSections" :key="sectionIndex" class="mb-8">
      <v-toolbar density="compact" color="grey-lighten-3" class="mb-0 rounded-t">
        <v-toolbar-title class="text-subtitle-1 font-weight-bold">{{ section.section_name }}</v-toolbar-title>
      </v-toolbar>

      <v-table density="compact" class="elevation-1 data-table">
        <thead>
          <tr>
            <th class="text-left sticky-col first-col model-details-header" :width="'15%'">Model</th>
            <th class="text-center model-details-header" :width="'5%'">Qty</th>
            <th class="text-right model-details-header" :width="'10%'">Est. AMV/Unit</th>
            <th class="text-center model-details-header" :width="'8%'">Colour %</th>
            <th class="text-center model-details-header" :width="'8%'">Oversize %</th>
            <th class="text-center model-details-header" :width="'10%'">Inclusive Plan</th>
            <th class="text-left metric-header" :width="'15%'">Term</th>
            <th class="text-left metric-header" :width="'10%'">Rate Type</th>
            <th
              v-for="metricDef in metricsDefinition"
              :key="metricDef.key"
              class="text-right metric-header"
            >
              {{ metricDef.name }}
            </th>
          </tr>
        </thead>
        <tbody>
          <template v-if="section.data_rows.length === 0">
            <tr>
              <td :colspan="8 + metricsDefinition.length" class="text-center pa-4">
                No data available for {{ section.section_name }}
              </td>
            </tr>
          </template>
          <template v-for="(modelItem, modelItemIndex) in section.data_rows" :key="`model-${sectionIndex}-${modelItem.Model}-${modelItemIndex}`">
            <template v-for="(serviceOpt, serviceOptIndex) in modelItem.serviceOptions" :key="`serviceOpt-${modelItem.Model}-${serviceOptIndex}-${serviceOpt.termLength}`">
              <!-- Row for Requested Rates -->
              <tr :class="{ 'model-start-row': serviceOptIndex === 0 && modelItemIndex > 0, 'service-option-start': true }">
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-left sticky-col first-col model-detail-cell">
                  {{ modelItem.Model }}
                </td>
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-center model-detail-cell">
                  {{ modelItem.Qty }}
                </td>
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-right model-detail-cell">
                  {{ modelItem['Estimated AMV / Unit'] }}
                </td>
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-center model-detail-cell">
                  {{ modelItem['Colour %'] }}
                </td>
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-center model-detail-cell">
                  {{ modelItem['Oversize %'] }}
                </td>
                <td v-if="serviceOptIndex === 0" :rowspan="modelItem.serviceOptions.length * 2" class="text-center model-detail-cell">
                  {{ modelItem['Inclusive Plan Yes/No'] }}
                </td>
                <td :rowspan="2" class="text-left metric-name-cell term-cell">{{ serviceOpt.termLength }}</td>
                <td class="text-left metric-name-cell">Requested</td>
                <td v-for="metricDef in metricsDefinition" :key="`req-${modelItem.Model}-${serviceOpt.termLength}-${metricDef.key}`" class="text-right value-cell">
                  {{ formatValue(serviceOpt.Requested[metricDef.key], metricDef.key) }}
                </td>
              </tr>

              <!-- Row for Approved Rates (Editable) -->
              <tr class="approved-row">
                <td class="text-left metric-name-cell">Approved</td>
                <td v-for="metricDef in metricsDefinition" :key="`app-${modelItem.Model}-${serviceOpt.termLength}-${metricDef.key}`" class="value-cell editable-cell">
                  <v-text-field
                    v-model="serviceOpt.Approved[metricDef.key]"
                    density="compact"
                    hide-details
                    variant="outlined"
                    :type="isNumericMetric(metricDef.key) ? 'number' : 'text'"
                    :step="isNumericMetric(metricDef.key) ? '0.00001' : undefined"
                    :placeholder="isNumericMetric(metricDef.key) ? '0.00000' : '-'"
                    class="approved-input"
                    :class="{ 'highlight-change': serviceOpt.Approved[metricDef.key] !== serviceOpt.Requested[metricDef.key] && serviceOpt.Requested[metricDef.key] !== null }"
                  ></v-text-field>
                </td>
              </tr>
            </template>
          </template>
        </tbody>
      </v-table>
    </div>

        <div class="d-flex justify-space-between align-center mt-4">
      <div>
        <v-btn color="secondary" @click="downloadExcelTemplate" class="mr-2">Download Template</v-btn>
        <v-btn color="secondary" @click="triggerFileUpload">Upload Template</v-btn>
        <input type="file" ref="fileInput" @change="handleFileUpload" style="display: none" accept=".xlsx, .xls" />
      </div>
      <v-btn color="primary" @click="submitApproval">Submit Approval</v-btn>
    </div>
  </v-container>
</template>

<script setup lang="ts">
import { ref, defineProps, defineEmits, watch, type PropType } from 'vue';
import * as XLSX from 'xlsx';
import ServiceApprovalHeader from './ServiceApprovalHeader.vue';
import { useSnackbarStore } from '@/stores/SnackbarStore';

// Define interfaces for our data structures
interface RateDetails {
  [key: string]: number | string | null;
  'B&W': number | null;
  'Colour': number | null;
  'Extra Long (IPRC only)': number | null;
  'Base $Amt': number | null;
  'Base Volume': number | null;
}

interface ServiceOption {
  termLength: string;
  Published?: RateDetails;
  Requested: RateDetails;
  Approved: RateDetails;
  serviceApprovalId: number;
  serviceFormId: number;
}

interface ModelItem {
  Model: string;
  Qty: number;
  'Estimated AMV / Unit': number;
  'Colour %': number;
  'Oversize %': number;
  'Inclusive Plan Yes/No': boolean | string;
  serviceOptions: ServiceOption[];
}

interface TableSection {
  section_name: string;
  data_rows: ModelItem[];
}

const emit = defineEmits([
  'update:term-length',
  'update:service-value-pack',
  'submit'
]);

const snackbarStore = useSnackbarStore();
const props = defineProps({
  termLength: {
    type: String,
    required: true
  },
  serviceValuePack: {
    type: String,
    required: true
  },
  serviceLevels: {
    type: Array as () => Array<{ label: string; value: string }>,
    default: () => []
  },
  mainHeaderValue: {
    type: String,
    default: "5"
  },
  sectionsData: {
    type: Array as PropType<TableSection[]>,
    required: true
  },
  customerName: {
    type: String,
    default: ''
  }
});

const fileInput = ref<HTMLInputElement | null>(null);

const triggerFileUpload = () => {
  fileInput.value?.click();
};

const RATE_KEYS = new Set(['B&W', 'Colour', 'Extra Long (IPRC only)']);

const formatValue = (value: any, key?: string, forExcel = false) => {
  if (value === null || value === undefined || value === '') return forExcel ? '' : '-';
  if (key && RATE_KEYS.has(key) && typeof value === 'number') {
    const fixed = value.toFixed(5);
    return fixed;
  }
  return value;
};

const downloadExcelTemplate = () => {

  const detailColumns = [
    'Model',
    'Term',
    'Qty',
    'Estimated AMV / Unit',
    'Colour %',
    'Oversize %',
    'Inclusive Plan Yes/No',
  ];

  // Build the flat data set ----------------------------------------------------------------
  const dataForExcel: any[] = [];

  tableSections.value.forEach(section => {
    section.data_rows?.forEach(modelItem => {
      modelItem.serviceOptions?.forEach(serviceOpt => {
        const base: { [key: string]: any } = {
          'Model': modelItem.Model,
          'Term': serviceOpt.termLength,
          'Qty': modelItem.Qty,
          'Estimated AMV / Unit': formatValue(modelItem['Estimated AMV / Unit']),
          'Colour %': formatValue(modelItem['Colour %']),
          'Oversize %': formatValue(modelItem['Oversize %']),
          'Inclusive Plan Yes/No': modelItem['Inclusive Plan Yes/No'],
        };

        metricsDefinition.value.forEach(metricDef => {
          // Requested: ensure 5-decimal strings for rate keys
          base[`Requested - ${metricDef.name}`] = formatValue(
            serviceOpt.Requested?.[metricDef.key],
            metricDef.key,
            true
          );
          // Approved: leave blank in template
          base[`Approved - ${metricDef.name}`] = '';
        });

        dataForExcel.push(base);
      });
    });
  });

  if (dataForExcel.length === 0) {
    snackbarStore.show({
      text: 'No data available to download.',
      color: 'error',
      timeout: 3000,
    });
    return;
  }

  // ----------------------------------------------------------------------------------------
  // 1️⃣  Build header rows (row 0 + row 1) --------------------------------------------------
  const metricNames = metricsDefinition.value.map((m) => m.name);
  const requestedHeaders = metricNames.map((n) => `Requested - ${n}`);
  const approvedHeaders  = metricNames.map((n) => `Approved - ${n}`);

  // Row 0: parent headers (we will merge cells later)
  const headerRow0: any[] = [];
  headerRow0.push('Model Details'); // will be merged across detailColumns.length columns
  // placeholder empty cells to be swallowed by merge
  for (let i = 1; i < detailColumns.length; i++) headerRow0.push(null);

  headerRow0.push('Requested');
  for (let i = 1; i < metricNames.length; i++) headerRow0.push(null);

  headerRow0.push('Approved');
  for (let i = 1; i < metricNames.length; i++) headerRow0.push(null);

  // Row 1: actual column headers
  const headerRow1 = [
    ...detailColumns,
    ...requestedHeaders,
    ...approvedHeaders,
  ];

  // ----------------------------------------------------------------------------------------
  // 2️⃣  Transform data rows into arrays in same order as headerRow1 ------------------------
  const dataRows = dataForExcel.map((rowObj) => headerRow1.map((key) => rowObj[key] ?? ''));

  // 3️⃣  Create worksheet via AoA and apply merges/styles -----------------------------------
  const worksheet = XLSX.utils.aoa_to_sheet([headerRow0, headerRow1, ...dataRows]);

  // Merges
  const merges: XLSX.Range[] = []; // s: start, e: end (row/col)
  // Customer Information merge
  merges.push({ s: { r: 0, c: 0 }, e: { r: 0, c: detailColumns.length - 1 } });
  // Requested
  merges.push({
    s: { r: 0, c: detailColumns.length },
    e: { r: 0, c: detailColumns.length + metricNames.length - 1 },
  });
  // Approved
  merges.push({
    s: { r: 0, c: detailColumns.length + metricNames.length },
    e: { r: 0, c: detailColumns.length + metricNames.length * 2 - 1 },
  });
  worksheet['!merges'] = merges;

  // Style parent header cells --------------------------------------------------------------
  const getCellAddress = (row: number, col: number) => XLSX.utils.encode_cell({ r: row, c: col });
  const styleCell = (address: string, bgRgb: string) => {
    const cell = worksheet[address];
    if (cell) {
      cell.s = {
        fill: { patternType: 'solid', fgColor: { rgb: bgRgb } },
        font: { bold: true },
        alignment: { horizontal: 'center', vertical: 'center' },
      } as any;
    }
  };

  // Grey for Published, Light Yellow for Requested, Lavender for Approved
  styleCell(getCellAddress(0, detailColumns.length), 'FFF2CC');
  styleCell(getCellAddress(0, detailColumns.length + metricNames.length), 'E4DFEC');

  // ----------------------------------------------------------------------------------------
  // 4️⃣  Column widths
  const colWidths = headerRow1.map((h) => ({ wch: Math.max(h.length, 15) }));
  worksheet['!cols'] = colWidths;

  // 5️⃣  Finalise workbook -----------------------------------------------------------------
  const workbook = XLSX.utils.book_new();
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Service Approval');

  // Filename: <customerName>-<YYYY-MM-DD>.xlsx
  const safeName = (props.customerName || 'ServiceApproval').replace(/[\\\/:*?"<>|]/g, '_');
  const d = new Date();
  const yyyy = d.getFullYear();
  const mm = String(d.getMonth() + 1).padStart(2, '0');
  const dd = String(d.getDate()).padStart(2, '0');
  const dateStr = `${yyyy}-${mm}-${dd}`;
  XLSX.writeFile(workbook, `${safeName}-${dateStr}.xlsx`);
};

const handleFileUpload = (event: Event) => {
  const target = event.target as HTMLInputElement;
  if (!target.files || target.files.length === 0) return;

  const file = target.files[0];
  const reader = new FileReader();

  reader.onload = (e) => {
    try {
      const data = e.target?.result;
      if (!data) throw new Error('No data');

      const workbook = XLSX.read(data, { type: 'array' });
      const sheetName = workbook.SheetNames[0];
      const worksheet = workbook.Sheets[sheetName];

      /*
        The exported template has a parent-header row (row 0) that only contains
        merged titles ("Customer Information", "Published", etc.).  The real
        column headers live in row 1.  XLSX allows us to skip the first row via
        the `range` option so that row 1 becomes the header row for the JSON
        conversion.
      */
      const jsonData: any[] = XLSX.utils.sheet_to_json(worksheet, { range: 1 });

      // Map display metric names -> keys (e.g. "B&W Rate" -> "B&W")
      const metricNameMap = new Map<string, string>();
      metricsDefinition.value.forEach((m) => metricNameMap.set(m.name, m.key));

      jsonData.forEach((row) => {
        const modelName = row['Model'];
        const term = row['Term'];
        if (!modelName || !term) {
          console.warn('Skipping row due to missing Model or Term:', row);
          return;
        }

        let matchFound = false;
        tableSections.value.forEach((section) => {
          section.data_rows.forEach((modelItem) => {
            if (modelItem.Model !== modelName) return;
            modelItem.serviceOptions.forEach((serviceOpt) => {
              if (String(serviceOpt.termLength) !== String(term)) return;

              matchFound = true;
              Object.keys(row).forEach((header) => {
                if (!header.startsWith('Approved - ')) return;
                const metricName = header.replace('Approved - ', '');
                const metricKey = metricNameMap.get(metricName);
                if (!metricKey) return;

                const uploadedValue = row[header];
                const isNumeric = isNumericMetric(metricKey);
                const parsedValue = isNumeric ? parseFloat(uploadedValue) : uploadedValue;
                serviceOpt.Approved[metricKey] = isNumeric && isNaN(parsedValue)
                  ? null
                  : parsedValue;
              });
            });
          });
        });

        if (!matchFound) {
          console.warn(`No match found in table for Model: ${modelName}, Term: ${term}`);
        }
      });

      snackbarStore.show({
        text: 'File processed successfully. Please review the changes.',
        color: 'success',
        timeout: 3000,
      });
    } catch (error) {
      console.error('Error processing file:', error);
      snackbarStore.show({
        text: 'There was an error processing the file. Please ensure it is a valid Excel template.',
        color: 'error',
        timeout: 3000,
      });
    }
  };

  reader.onerror = (error) => {
    console.error('FileReader error:', error);
    snackbarStore.show({
      text: 'Failed to read the file.',
      color: 'error',
      timeout: 3000,
    });
  };

  reader.readAsArrayBuffer(file);

  // Reset file input to allow re-uploading the same file
  target.value = '';
};


// Define the metrics and their display names/keys
const metricsDefinition = ref([
  { key: 'B&W', name: 'B&W Rate', numeric: true },
  { key: 'Colour', name: 'Colour (LTD/OS) Rate', numeric: true },
  { key: 'Extra Long (IPRC only)', name: 'Extra Long (IPRC only)', numeric: true },
  { key: 'Base $Amt', name: 'Base $Amount', numeric: true },
  { key: 'Base Volume', name: 'Base Volume', numeric: true }
]);

// Create a reactive copy of the sections data
const tableSections = ref<TableSection[]>(JSON.parse(JSON.stringify(props.sectionsData)));

const isNumericMetric = (key: string) => {
  const metric = metricsDefinition.value.find(m => m.key === key);
  return metric ? metric.numeric : false;
};

// formatValue moved above and enhanced

const submitApproval = () => {
  emit('submit', tableSections.value);
};

// Optional: Watch for external changes to sectionsData if needed
watch(() => props.sectionsData, (newVal) => {
  tableSections.value = JSON.parse(JSON.stringify(newVal));
}, { deep: true });
</script>

<style scoped>
.data-table th, .data-table td {
  border: 1px solid #e0e0e0;
  padding: 6px 10px !important;
}

.model-details-header, .metric-header {
  background-color: #f5f5f5;
  font-weight: bold;
  font-size: 0.9em;
}
.metric-header {
  background-color: #efefef;
}

.sticky-col {
  position: -webkit-sticky;
  position: sticky;
  left: 0;
  z-index: 2;
  background-color: white;
}
.data-table thead .sticky-col {
  z-index: 3;
}
.data-table .model-start-row td {
  border-top: 2px solid #c0c0c0 !important;
}
.data-table .model-start-row .sticky-col.model-detail-cell {
  border-top: 2px solid #c0c0c0 !important;
}

.model-detail-cell {
  background-color: #f9f9f9;
  vertical-align: top;
}
.metric-name-cell {
  font-style: italic;
  color: #333;
}
.value-cell {
  vertical-align: middle;
}
.editable-cell .v-text-field {
  min-width: 100px;
}
.approved-input :deep(input) {
  text-align: right;
  padding-left: 2px !important;
  padding-right: 2px !important;
}
.approved-input.highlight-change :deep(input) {
  background-color: #fff59d;
  font-weight: bold;
}
.v-table {
  overflow-x: auto;
}

/* Hide spinners from number input fields */
.approved-input input[type="number"]::-webkit-inner-spin-button,
.approved-input input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}

.approved-input :deep(input[type=number]) {
  -moz-appearance: textfield; /* Firefox */
  appearance: none;
}
.approved-input input[type="number"]::-webkit-inner-spin-button,
.approved-input input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  appearance: none;
  margin: 0;
}
</style>
