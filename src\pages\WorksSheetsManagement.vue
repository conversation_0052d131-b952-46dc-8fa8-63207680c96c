<script setup lang="ts">
/**
 * @file WorkSheets Management page.
 * @version 1.0.0
 * @since 1.0.0
 */

/**
 * -------
 * Imports
 * -------
 */
import { ref, onMounted, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { useAppStore } from '@/stores/AppStore';
import type {  WorkSheetProduct } from '@/lib/common/types';
import WorksheetTable from '@/components/worksheets/WorksheetTable.vue';

import { getWorksheetData, getSoftwareWorksheetData } from '@/lib/api';

/**
 * ----
 * Main
 * ----
 */
// Add stores
const appStore = useAppStore();

// Add language support
const { t } = useI18n();

// Tab state
const currentTab = ref('hardware');

// Track which tabs have been loaded
const loadedTabs = ref<Set<string>>(new Set());

// Loading state
const loading = ref(false);

// Raw data from API - separate for hardware and software
const rawHardwareData = ref<WorkSheetProduct[]>([]);
const rawSoftwareData = ref<WorkSheetProduct[]>([]);
const hardwareData = ref<WorkSheetProduct[]>([]);
const softwareData = ref<WorkSheetProduct[]>([]);

// Transform API data to display format
const transformApiData = (apiData: WorkSheetProduct[]): WorkSheetProduct[] => {
  return apiData.map(item => ({
    id: item.id,
    itemId: item.itemId,
    parentItemId: item.parentItemId,
    itemNumber: item.itemNumber,
    displayName: item.displayName,
    sellByDsd: item.sellByDsd,
    sellByDealer: item.sellByDealer,
    isSolution: item.isSolution,
    isSolutionGroup: item.isSolutionGroup,
    description: item.description,
    modelName: item.modelName,
    isActive: item.isActive,
    isTerminated: item.isTerminated,
    isMainUnit: item.isMainUnit,
    isMainframe: item.isMainframe,
    itemPriceId: item.itemPriceId,
    currency: item.currency,
    msrp: item.msrp,
    wholesaleCost: item.wholesaleCost
  }));
};

// API call to fetch hardware worksheet data
const fetchHardwareData = async () => {
  if (loadedTabs.value.has('hardware')) return; // Skip if already loaded

  loading.value = true;
  try {
    const response = await getWorksheetData();
    rawHardwareData.value = response.data;
    hardwareData.value = transformApiData(response.data);
    loadedTabs.value.add('hardware');
  } catch (error) {
    console.error('Error fetching hardware worksheet data:', error);
  } finally {
    loading.value = false;
  }
};

// API call to fetch software worksheet data
const fetchSoftwareData = async () => {
  if (loadedTabs.value.has('software')) return; // Skip if already loaded

  loading.value = true;
  try {
    const response = await getSoftwareWorksheetData();
    rawSoftwareData.value = response.data;
    softwareData.value = transformApiData(response.data);
    loadedTabs.value.add('software');
  } catch (error) {
    console.error('Error fetching software worksheet data:', error);
  } finally {
    loading.value = false;
  }
};

// Fetch data based on current tab
const fetchDataForCurrentTab = async () => {
  if (currentTab.value === 'hardware') {
    await fetchHardwareData();
  } else if (currentTab.value === 'software') {
    await fetchSoftwareData();
  }
};

// Handle table actions
const handleTableAction = ({ action, item }: { action: string; item: WorkSheetProduct }) => {
  switch (action) {
    case 'add':
      console.log('Add new item:', item);
      // TODO: Implement add functionality
      break;
    case 'edit':
      console.log('Edit item:', item);
      // TODO: Implement edit functionality
      break;
    case 'delete':
      console.log('Delete item:', item);
      // TODO: Implement delete functionality
      break;
    default:
      console.log('Unknown action:', action, item);
  }
};

// Watch for tab changes to fetch appropriate data
watch(currentTab, () => {
  fetchDataForCurrentTab();
}, { immediate: false });

// Load data on component mount
onMounted(() => {
  fetchDataForCurrentTab();
  // Stop page loader when component is mounted
  appStore.stopPageLoader();
});
</script>

<template>
  <div class="pa-4">
    <div class="d-flex justify-space-between align-center mb-4">
      <h1>{{ t('page.worksheets_management.title') }}</h1>
      <v-btn color="primary" prepend-icon="add">
        {{ t('page.worksheets_management.button.new_worksheet') }}
      </v-btn>
    </div>

    <v-card class="mt-4">
      <v-tabs v-model="currentTab" grow class="mb-0">
        <v-tab value="hardware">
          <v-icon start>hardware</v-icon>
          {{ t('page.worksheets_management.tabs.hardware') }}
        </v-tab>
        <v-tab value="software">
          <v-icon start>apps</v-icon>
          {{ t('page.worksheets_management.tabs.software') }}
        </v-tab>
      </v-tabs>

      <v-divider></v-divider>

      <v-card-text>
        <v-window v-model="currentTab">
          <v-window-item value="hardware">
            <WorksheetTable
              :data="hardwareData"
              :loading="loading"
              category="hardware"
              @action="handleTableAction"
            />
          </v-window-item>

          <v-window-item value="software">
            <WorksheetTable
              :data="softwareData"
              :loading="loading"
              category="software"
              @action="handleTableAction"
            />
          </v-window-item>
        </v-window>
      </v-card-text>
    </v-card>
  </div>
</template>