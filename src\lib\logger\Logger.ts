/**
 * @file Logging for development mode.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @@license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * Displays message to console if in developer mode.
 *
 * @param {*} value
 * @param {string} message
 */
const Logger = ( value : any, message? : string | null ) : void =>
{
    if ( import.meta.env.VITE_APP_DEV.toUpperCase() === 'TRUE' )
    {
        // Output the message.
        if ( message )
        {
            console.log( message );
        }

        // Output the value.
        console.log( value );
    }
}

export default Logger;