<script setup lang="ts">
import { reactive, watch, defineEmits, defineProps, onMounted } from 'vue';
import { getCmacRegistration, saveCmacRegistration, type SaveCmacRegistrationPayload } from '@/services/salesRequestService';

// --- Data Definitions ---
const sbiOptions = [
  { category: 'S1', commitment: '$1 Million +', term: '12 months' },
  { category: 'S2', commitment: '$2 Million +', term: '24 months' },
  { category: 'S2Y', commitment: '$4.5 Million +', term: '24 months' },
];

const sasOptions = [
  { category: 'SA', code: 'SA', commitment: '$100,000+', term: 'Per Approval' },
  { category: 'GA', code: 'GA', commitment: 'no commitment', term: 'Per Approval' },
  { category: 'SH', code: 'SH', commitment: 'no commitment', term: 'Per Approval' },
  { category: 'CK', code: 'CK', commitment: '$100,000+', term: 'Per Approval' },
];

// --- Component Props and Emits ---
const emit = defineEmits(['update:modelValue']);
const props = defineProps({
  modelValue: {
    type: Object as () => any,
    required: true,
  },
  status: {
    type: Number,
    required: false,
    default: null,
  },
  requestId: {
    type: Number,
    required: false,
    default: null,
  }
});

// Fetch existing CMAC registration when component mounts
onMounted(async () => {
  if (props.requestId) {
    try {
      const { data } = await getCmacRegistration(props.requestId);
      if (data) {
        mapApiToLocal(data);
      }
    } catch (e) {
      console.error('Failed to load CMAC registration', e);
    }
  }
});

// Expose getter for parent component
function getFormData() {
  return JSON.parse(JSON.stringify(localForm));
}

import { ref } from 'vue';
const saving = ref(false);

async function handleSubmit() {
  if (!props.requestId) {
    console.error('requestId prop missing');
    return;
  }
  const payload: SaveCmacRegistrationPayload = {
    requestId: props.requestId,
    locationType: localForm.customer_info.type_of_location[0] || null,
    customerType: localForm.customer_info.customer_type || null,
    subsidaryInfo: localForm.customer_info.subsidiaries || null,
    requestedStartDate: localForm.pricing_selection.requested_start_date || null,
    requestTermInMonth: null,
    registrationSelection: localForm.pricing_selection.category || null,
  };
  try {
    saving.value = true;
    await saveCmacRegistration(payload);
    console.log('CMAC registration saved');
  } catch (e) {
    console.error('Failed to save CMAC registration', e);
  } finally {
    saving.value = false;
  }
}

function mapApiToLocal(apiData: any) {
  // Customer information
  if (apiData.customer) {
    localForm.customer_info.name = apiData.customer.displayName || apiData.customer.businessName || '';
    // Address not provided in sample; keep defaults
  }
  localForm.customer_info.type_of_location = apiData.locationType ? [apiData.locationType] : [];
  localForm.customer_info.customer_type = apiData.customerType || '';
  localForm.customer_info.subsidiaries = apiData.subsidaryInfo || '';

  // Pricing selection
  localForm.pricing_selection.category = apiData.registrationSelection || null;
  if (apiData.requestedStartDate) {
    localForm.pricing_selection.requested_start_date = apiData.requestedStartDate.substring(0, 10); // YYYY-MM-DD
  }

  // Channel info
  localForm.channel_info.branch_name = apiData.salesBranchName || '';
  localForm.channel_info.rep_name = apiData.salesRepName || '';
}

defineExpose({ getFormData, handleSubmit });

// --- Reactive State ---
const localForm = reactive({
  customer_info: {
    name: '',
    address: '',
    address2_3: '',
    city_prov_postal: '',
    contact_name: '',
    title: '',
    telephone: '',
    fax: '',
    type_of_location: [] as string[],
    customer_type: '',
    subsidiaries: '',
  },
  pricing_selection: {
    category: '' as string | null, // Can be 'S1', 'SA', etc.
    requested_start_date: null as string | null,
  },
  channel_info: {
    branch_name: '',
    rep_name: '',
    approved_by: '',
    signature: '',
    title: '',
    date: null as string | null,
  },
});

// Initialize localForm with prop value and handle legacy structures
if (props.modelValue) {
  Object.assign(localForm, props.modelValue);
  // Simple migration from old data structure to new one
  if (props.modelValue.sbi_selection?.category) {
    localForm.pricing_selection.category = props.modelValue.sbi_selection.category;
  } else if (props.modelValue.sas_selection?.category) {
    localForm.pricing_selection.category = props.modelValue.sas_selection.category;
  }
}

// Watch for changes and emit upwards
watch(
  localForm,
  (val) => {
    // Clean up legacy properties before emitting
    const output = { ...val };
    delete (output as any).sbi_selection;
    delete (output as any).sas_selection;
    emit('update:modelValue', output);
  },
  { deep: true }
);
import { computed } from 'vue';

// Determine if overlay should be shown (status < 6)
const showNotApprovedOverlay = computed(() => typeof props.status === 'number' && props.status < 6);
</script>

<template>
  <v-container fluid class="bg-white pa-6 position-relative">
    <v-row justify="space-between" align="center">
      <v-col cols="auto">
        <!-- Canon Logo can be an img tag if available -->
        <h2 class="text-h5">Canon</h2>
      </v-col>
      <v-col cols="auto">
        <v-btn variant="outlined">Export CMAC Registration</v-btn>
      </v-col>
    </v-row>

    <div class="text-center my-4">
      <h1 class="text-h6 font-weight-bold">Canon Major Account Commitment (CMAC)</h1>
      <h2 class="text-subtitle-1 font-weight-bold">Account Registration Form</h2>
    </div>

    <!-- Customer Information -->
    <v-card flat variant="outlined" class="pa-4">
      <v-card-title class="text-body-1 font-weight-bold">Customer Information <span class="text-red font-weight-regular">(Do not use acronyms - Use full name only)</span></v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="8">
            <v-text-field v-model="localForm.customer_info.name" label="Name" density="compact" variant="outlined" />
            <v-text-field v-model="localForm.customer_info.address" label="Address" density="compact" variant="outlined" />
            <v-text-field v-model="localForm.customer_info.address2_3" label="Address 2/3" density="compact" variant="outlined" />
            <v-text-field v-model="localForm.customer_info.city_prov_postal" label="City/Prov./Postal Code" density="compact" variant="outlined" />
            <v-row>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.contact_name" label="Contact Name" density="compact" variant="outlined" /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.title" label="Title" density="compact" variant="outlined" /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.telephone" label="Telephone #" density="compact" variant="outlined" /></v-col>
              <v-col cols="6"><v-text-field v-model="localForm.customer_info.fax" label="Fax #" density="compact" variant="outlined" /></v-col>
            </v-row>
          </v-col>
          <v-col cols="12" md="4">
            <label class="text-body-2">Type of Location:</label>
            <v-checkbox-group v-model="localForm.customer_info.type_of_location" inline>
              <v-checkbox label="HQ" value="HQ" density="compact" />
              <v-checkbox label="Branch" value="Branch" density="compact" />
              <v-checkbox label="Subsidiary" value="Subsidiary" density="compact" />
            </v-checkbox-group>
            <v-text-field v-model="localForm.customer_info.customer_type" label="Customer Type" density="compact" variant="outlined" />
            <v-textarea v-model="localForm.customer_info.subsidiaries" :maxlength="500"  label="Subsidiaries" density="compact" variant="outlined" rows="5" />
          </v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <v-alert type="info" variant="tonal" density="compact" class="my-4 text-center font-weight-bold">
      **PLEASE REVIEW YOUR PRICING APPROVAL FOR THE CORRECT CATEGORY**
    </v-alert>

    <!-- Pricing Selection -->
    <v-radio-group v-model="localForm.pricing_selection.category">
      <v-row>
        <v-col cols="12">
          <div class="text-body-1 font-weight-bold">Strategic Pricing Requests Over $1 Million in MSRP (SBI)</div>
          <v-table density="compact">
            <thead>
              <tr>
                <th class="font-weight-bold">Category</th>
                <th class="font-weight-bold">MSRP Commitment</th>
                <th class="font-weight-bold">Term</th>
                <th class="font-weight-bold text-center">Registration Selection (with "X")</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in sbiOptions" :key="item.category">
                <td>{{ item.category }}</td>
                <td>{{ item.commitment }}</td>
                <td>{{ item.term }}</td>
                <td class="text-center"><v-radio :value="item.category"></v-radio></td>
              </tr>
            </tbody>
          </v-table>
        </v-col>

        <v-col cols="12">
          <div class="text-body-1 font-weight-bold">Strategic Pricing Requests Over $100,000 and Under $1 Million in MSRP (SAS)</div>
           <v-table density="compact">
            <thead>
              <tr>
                <th class="font-weight-bold">Category</th>
                <th class="font-weight-bold">CODE</th>
                <th class="font-weight-bold">MSRP Commitment</th>
                <th class="font-weight-bold">Term</th>
                <th class="font-weight-bold text-center">Registration Selection (with "X")</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="item in sasOptions" :key="item.category">
                <td>{{ item.category === 'GA' ? 'Special SAS (Global)**' : item.category === 'SA' ? 'Special SAS' : item.category }}</td>
                <td>{{ item.code }}</td>
                <td>{{ item.commitment }}</td>
                <td>{{ item.term }}</td>
                <td class="text-center"><v-radio :value="item.category"></v-radio></td>
              </tr>
            </tbody>
          </v-table>
          <div class="text-red text-caption mt-1">**Global Deals must have a global agreement with Canadian Pricing</div>
        </v-col>

        <v-col cols="12" md="4">
            <v-text-field v-model="localForm.pricing_selection.requested_start_date" label="Requested Start Date" type="date" density="compact" variant="outlined" hint="YYYY/MM/DD"/>
        </v-col>
      </v-row>
    </v-radio-group>

    <v-alert type="warning" variant="text" class="my-4 font-weight-bold text-blue-darken-3">
      **Please note that the pricing term is subject to change based on any general announcements regarding LRF's or price increases.**
    </v-alert>

    <!-- Terms and Conditions -->
    <v-card flat class="my-4">
      <v-card-title class="text-body-1 font-weight-bold">Terms and Conditions:</v-card-title>
      <v-card-text class="text-body-2">
        By signing below, Channel confirms receipt of the CMAC Terms and Conditions, and agrees that all of the terms and conditions of the CMAC program, including the section governing charge backs, are incorporated and made a part of this Registration Form. The CMAC guidelines and this Registration Form, are intended to be the entire agreement between Canon Canada and Channel with respect to Canon Canada's bid support to channel for the customer named herein, shall not be effective until this Registration Form has been signed by both Channel and Canon Canada.
      </v-card-text>
    </v-card>

    <!-- Channel Information -->
    <v-card flat variant="outlined" class="pa-4">
      <v-card-title class="text-body-1 font-weight-bold">Channel Information</v-card-title>
      <v-card-text>
        <v-row>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.branch_name" label="Branch Name" density="compact" variant="outlined" /></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.approved_by" label="Approved By" density="compact" variant="outlined" /></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.rep_name" label="Rep Name" density="compact" variant="outlined" /></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.signature" label="Signature" density="compact" variant="outlined" hint="Signature field"/></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.title" label="Title" density="compact" variant="outlined" /></v-col>
          <v-col cols="12" md="6"><v-text-field v-model="localForm.channel_info.date" label="Date" type="date" density="compact" variant="outlined" /></v-col>
        </v-row>
      </v-card-text>
    </v-card>

    <div class="text-center font-weight-bold mt-6">
      **PLEASE NOTE, IT TAKES 24-48 HOURS FOR THE REGISTRATION NUMBER TO APPEAR IN THE SYSTEM**
    </div>

    <!-- Submit Button -->
    <v-row class="mt-6" justify="center">
      <v-btn color="primary" :loading="saving" @click="handleSubmit">
        Save CMAC Registration
      </v-btn>
    </v-row>

    <!-- Overlay when not yet approved -->
  <div v-if="showNotApprovedOverlay" class="not-approved-overlay d-flex align-center justify-center">
      <span class="overlay-text">NOT YET APPROVED</span>
  </div>
</v-container>
</template>

<style scoped>
.v-table th {
  white-space: nowrap;
}
.position-relative {
  position: relative;
}

.not-approved-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(128, 128, 128, 0.6);
  z-index: 10;
  pointer-events: all;
}

.overlay-text {
  display: block;
  font-size: 6vw; /* responsive large size */
  font-weight: 900;
  color: #ffffff;
  text-shadow: 2px 2px 4px #000000;
  transform: rotate(-25deg);
  user-select: none;
}

</style>
