<template>
  <v-card class="mb-5">
    <v-card-title class="text-h5">
      Service Approval
    </v-card-title>
    <v-card-text>
      <v-row dense>
        <v-col cols="12" md="4">
          <v-select
            v-model="localTermLength"
            :items="[1, 2, 3, 4, 5]"
            label="Term Length (Years)"
          ></v-select>
        </v-col>
        <v-col cols="12" md="4">
          <v-select
            v-model="localServiceValuePack"
            :items="serviceLevelItems"  
            label="Service Value Pack"
            item-title="label"
            item-value="value"
          ></v-select>
        </v-col>
      </v-row>
    </v-card-text>
  </v-card>
</template>

<script setup lang="ts">
import { computed, ref, watch } from 'vue';

const props = defineProps({
  termLength: {
    type: [String, Number],
    required: true
  },
  serviceValuePack: {
    type: String,
    required: true
  },
  serviceLevels: {
    type: Array as () => Array<{ label: string; value: string }>,
    default: () => []
  },
});

const emit = defineEmits<{
  'update:termLength': [value: number],
  'update:serviceValuePack': [value: string]
}>();

const localTermLength = computed({
  get: () => props.termLength,
  set: (value) => emit('update:termLength', Number(value))
});

const localServiceValuePack = computed({
  get: () => props.serviceValuePack,
  set: (value) => emit('update:serviceValuePack', value)
});

const serviceLevelItems = computed(() => props.serviceLevels);

</script>

<style scoped>
/* Add any header-specific styles here */
</style>
