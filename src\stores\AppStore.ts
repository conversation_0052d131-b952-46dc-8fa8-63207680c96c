/**
 * @file Pinia store defining global state for the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import type { AppStoreState } from '@/lib/common/types';

import { defineStore } from 'pinia';
import { updateUserProfile } from '@/lib/api';
import { useUserStore } from './UserStore';
import Logger from '@/lib/logger/Logger';

/**
 * ----
 * Main
 * ----
 */

// Gets settings from local storage.
let cacheTheme = localStorage.getItem( 'appTheme' );
let cacheLanguage = localStorage.getItem( 'appLanguage' );

/**
 * ------
 * Export
 * ------
 */

export const useAppStore = defineStore( 'AppStore',
{
    /**
	 * Store state variables.
	 */
    state : () : AppStoreState =>
    {
        return {
            appInit: false,
            loadingQueue: [ { id: 'AppInit', message: '' } ],
            loading: true,
            pageLoader: true,
            currentTheme: cacheTheme ? cacheTheme : 'appThemeLight',
            currentLanguage: cacheLanguage ? cacheLanguage : 'en-US',
        }
    },

    /**
	 * Store actions.
	 */
    actions :
    {
        /**
         * Adds a loader item to the app loading queue.
         *
         * @param {string} uid Unique ID for the loader message.
         * @param {string} [text] Message to display (optional).
         */
        startLoader ( uid : string, text? : string ) : void
        {
            this.loadingQueue.unshift( { message : text, id : uid } );
        },

        /**
         * Turns off the page loader.
         *
         */
        startPageLoader (): void
        {
            this.pageLoader = true;
        },

        /**
         * Turns off the page loader.
         *
         */
        stopPageLoader (): void
        {
            // The timer allows the visual loading element to appear and disappear smoothly for pages that load quickly.
            setTimeout( () =>
            {
                this.pageLoader = false;
            }, 300);
        },

        /**
         * Removes a loader item from the queue according it the passed ID.
         *
         * @param {string} id
         */
        stopLoader ( id? : string )
        {
            if ( this.loadingQueue.length > 0 )
            {
                if ( id )
                {
                    // Find the array index of the item to remove.
                    const indexOfID = this.loadingQueue.findIndex( ( message ) => message.id == id );

                    // Remove the loading message with the passed ID.
                    this.loadingQueue.splice( indexOfID, 1 );
                }
                else
                {
                    this.loadingQueue.splice( 0, this.loadingQueue.length );
                }
            }

            if ( this.loadingQueue.length == 0 )
            {
                this.loading = false;
            }
        },

        /**
         * Sets theme based on passed value (if valid).
         *
         * @param {string} value
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setTheme ( value : string, pushUpdate : boolean = false )
        {
            if ( value === 'light' )
            {
                this.setThemeLight( pushUpdate );
            }
            
            if ( value === 'dark' )
            {
                this.setThemeDark( pushUpdate );
            }
        },

        /**
         * Changes the current theme to light and saves to cache.
         *
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setThemeLight ( pushUpdate : boolean = false )
        {
            this.currentTheme = "appThemeLight";

            // Set browser local cache value.
            localStorage.setItem('appTheme', 'appThemeLight');

            if ( pushUpdate )
            {
                const userStore = useUserStore();

                if ( userStore.microsoftID )
                {
                    // Update user profile in backend.
                    updateUserProfile( userStore.microsoftID, { theme: 'light' }).catch( ( e ) =>
                    {
                        Logger( `System Error: Could not save user preferences to database. ${ e.message }` );
                    });
                }
            }
        },

        /**
         * Changes the current theme to dark and saves to cache.
         *
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setThemeDark ( pushUpdate : boolean = false )
        {
            this.currentTheme = "appThemeDark";

            // Set browser local cache value.
            localStorage.setItem('appTheme', 'appThemeDark');

            if ( pushUpdate )
            {
                const userStore = useUserStore();

                if ( userStore.microsoftID )
                {
                    // Update user profile in backend.
                    updateUserProfile( userStore.microsoftID, { theme: 'dark' }).catch( ( e ) =>
                    {
                        Logger( `System Error: Could not save user preferences to database. ${ e.message }` );
                    });
                }
            }
        },

        /**
         * Sets language based on passed value (if valid).
         * 
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setLanguage ( value : string, pushUpdate : boolean = false )
        {
            if ( value === 'en-US' )
            {
                this.setLanguageEnglish();
            }
            
            if ( value === 'fr-CA' )
            {
                this.setLanguageFrench();
            }
        },

        /**
         * Changes the users preferred language to English.
         * 
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setLanguageEnglish ( pushUpdate : boolean = false )
        {
            // Update app store setting.
            this.currentLanguage = "en-US";

            // Update browser local cache value.
            localStorage.setItem('appLanguage', 'en-US');

            // Update app language.
            this.i18n.global.locale = 'en-US';

            if ( pushUpdate )
            {
                const userStore = useUserStore();

                if ( userStore.microsoftID )
                {
                    // !! TODO !! Update this to your API.
                    // Update user profile in backend.
                    updateUserProfile( userStore.microsoftID, { language: 'en-US' } ).catch( ( e ) =>
                    {
                        Logger( `System Error: Could not save user preferences to database. ${ e.message }` );
                    });
                }
            }
        },

        /**
         * Changes the users preferred language to French.
         * 
         * @param {boolean} [pushUpdate=false] Flag indicating if value should be sent to update user profile.
         */
        setLanguageFrench ( pushUpdate : boolean = false )
        {
            // Update app store setting.
            this.currentLanguage = "fr-CA";

            // Update browser local cache value.
            localStorage.setItem('appLanguage', 'fr-CA');

            // Update app language.
            this.i18n.global.locale = 'fr-CA';

            if ( pushUpdate )
            {
                const userStore = useUserStore();

                if ( userStore.microsoftID )
                {
                    // Update user profile in backend.
                    updateUserProfile( userStore.microsoftID, { language: 'fr-CA' } ).catch( ( e ) =>
                    {
                        Logger( `System Error: Could not save user preferences to database. ${ e.message }` );
                    });
                }
            }
        }
    }
}); 