/**
 * @file Pinia store defining state for snack bars in the application.
 * @version 1.0.0
 * @since 1.0.0
 * <AUTHOR> <<EMAIL>>
 * @license Copyright (c) 2024 Canon Canada Inc.
 */

/**
 * -------
 * Imports
 * -------
 */

import { defineStore } from 'pinia';
import type { SnackbarStoreState, AppSnackbar } from '@/lib/common/types';

/**
 * ------
 * Export
 * ------
 */

export const useSnackbarStore = defineStore( 'SnackbarStore',
{
    /**
	 * Store state variables.
	 */
    state : () : SnackbarStoreState =>
    {
        return {
            showSnackbar : false,
            text: '',
            icon: '',
            timeout : 5000,
            close : false,
            location : 'top end',
            position : 'absolute',
            color : 'primary',
            variant : 'elevated'
        }
    },

    /**
	 * Store actions.
	 */
    actions :
    {
        /**
         * Display the snackbar using the provided parameters.
         * 
         * @param { AppSnackbar } [snackbarDetails] - Snackbar object.
         */
        show ( snackbarDetails : AppSnackbar ) : void
        {
            this.text = snackbarDetails.text;
            this.icon = snackbarDetails.icon;
            this.timeout = snackbarDetails.timeout || -1;
            this.close = snackbarDetails.close || false;
            this.location = snackbarDetails.location || 'top end';
            this.position = snackbarDetails.position || 'relative';
            this.color = snackbarDetails.color || 'surface';
            this.variant = snackbarDetails.variant || 'elevated';

            // Display the snackbar.
            this.showSnackbar = true;
        },

        /**
         * Hides the snackbar.
         */
        hide ()
        {
            // Hide the message.
            this.showSnackbar = false;

            this.text = '';
            this.icon = '';
            this.timeout = 5000;
            this.close = false;
            this.location = 'top end';
            this.position = 'relative';
            this.color = 'primary';
            this.variant = 'elevated';
        }
    }
}); 