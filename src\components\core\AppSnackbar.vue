<script setup lang="ts">
	/**
	 * @file Snackbar component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

	import { useSnackbarStore } from '@/stores/SnackbarStore';
    import { useI18n } from 'vue-i18n';

    /**
     * ----
     * Main
     * ----
     */

	// Add stores.
	const snackbarStore = useSnackbarStore();

    // Add language support.
    const { t } = useI18n();
</script>

<template>
	<v-snackbar v-model="snackbarStore.showSnackbar" :timeout="snackbarStore.timeout" :location="(snackbarStore.location)" :position="snackbarStore.position" :color="snackbarStore.color" :variant="snackbarStore.variant">
        <div class="d-flex align-center">
            <v-icon v-if="snackbarStore.icon" class="mr-3" size="32">{{ snackbarStore.icon }}</v-icon>

            {{ snackbarStore.text }}
        </div>

		<template v-slot:actions>
			<v-btn v-if="snackbarStore.close" variant="text" @click="snackbarStore.hide">{{ t( 'component.appsnackbar.button.close.label' ) }}</v-btn>
		</template>
	</v-snackbar>
</template>

<style lang="scss"></style>