<script setup lang="ts">
	/**
	 * @file Loader component.
	 * @version 1.0.0
	 * @since 1.0.0
	 * <AUTHOR> <<EMAIL>>
	 * @license Copyright (c) 2024 Canon Canada Inc.
	 */

    /**
     * -------
     * Imports
     * -------
     */

    import { ref, watch } from 'vue';
    import { useAppStore } from '@/stores/AppStore';

    /**
     * ----
     * Main
     * ----
     */
	
	// Add stores.
	const appStore = useAppStore();

    let message = ref( '' );

    // Watches for when the page is viewed in mobile resolution and disables the drawer if left open.
    watch( appStore.loadingQueue, async ( new_loadingQueue ) =>
    {
        if ( new_loadingQueue.length > 0 )
        {
            // Message to display.
            if ( new_loadingQueue[0].message )
            {
                message.value = new_loadingQueue[0].message;
            }
            // No message to display.
            else
            {
                message.value = '';
            }
            
            // Show the message.
            appStore.loading = true;
        }
    }, { immediate: true } );
</script>

<template>
	<v-dialog v-model="appStore.loading" hide-overlay persistent>
		<v-card color="background" min-width="300" dark class="ma-auto">
			<v-card-item class="py-0" :class="message ? 'mt-1' : ''">
				<v-card-text class="text-center text-overline py-2">
					{{ message }}
					<v-progress-linear indeterminate rounded reverse color="onBackground" class="my-2"></v-progress-linear>
				</v-card-text>
			</v-card-item>
		</v-card>
	</v-dialog>
</template>

<style lang="scss"></style>
