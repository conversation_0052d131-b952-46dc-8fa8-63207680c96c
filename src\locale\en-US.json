{"app": {"title": "Channel Support App", "auth": {"snack": {"success": "You have been successfully logged in."}, "loader": {"logging": "Logging In ..", "logout": "Logging Out ..", "redirect": "Redirecting to Microsoft ..", "authenticating": "Authenticating ..", "checkingUserDetails": "Checking User Details .."}, "error": {"title_1": "Access Restricted", "title_2": "Not Signed In", "title_3": "No Access", "title_4": "Critical Authentication Error", "title_5": "Unknown Error", "title_6": "Login Expired", "title_7": "Account Disabled", "desc_1": "You do not have permission to access the requested content.", "desc_2": "You could not be signed in to the application.", "desc_3": "Your sign-in request was successful, however, your account has not been granted permissions to access this application.<br><br>You have been automatically signed out.", "desc_4": "There was an unknown failure while attempting to log you in.", "desc_5": "There was a problem authenticating your login status. If you were previously logged in, you have been logged out.", "desc_6": "There was a problem authenticating your request. Your login session may have expired and have been logged out of this application. Sign-in and try again.", "desc_7": "Your account has been disabled and cannot be logged in.", "sub_1": "Role not assigned", "sub_2": "Authentication Check Failed", "sub_3": "Single Sign On"}, "button": {"go_home": {"label": "Go Home"}}}}, "channelS": {"form": {"employeeNameOrId": "Employee Name or ID", "employeeDetails": {"name": "Name", "department": "Department", "id": "ID", "e-mail": "E-mail"}}}, "page": {"404": {"header": "Page Not Found", "body": "The page you are looking for cannot be found."}, "home": {"user": {"header": "Welcome to the Channel support app", "desc": "You are now signed in."}, "anonymous": {"header": "Welcome to the Channel support app", "desc": "To begin, please sign-in.", "button": {"signin": {"label": "Sign In"}}}}, "role_management": {"title": "Role Management", "table": {"title": "Roles", "header": {"id": "ID", "description": "Description", "users": "Users"}}, "button": {"add_role": "Add Role"}}, "sales_requests": {"title": "Sales Requests", "table": {"title": "Sales Request List", "header": {"id": "ID", "customer": "Customer", "product": "Product", "status": "Status", "date": "Date"}}, "button": {"new_request": "New Sales Request"}}, "price_desk": {"title": "Price Desk", "table": {"title": "Price List", "header": {"request_number": "Request Number", "customer": "Customer", "created_by": "Created By", "last_updated": "Last Updated", "status": "Status"}}, "button": {"add_price": "Add Price"}}, "sales_request_form": {"title": "New Sales Request", "title_2": "Edit Sales Request", "title_3": "View Sales Request", "form": {"title": "Request Details"}, "tabs": {"customer_details": "Customer Details", "equipments": "Strategy", "worksheets": "Worksheets", "payment_details": "Payment Details"}, "customer_details": {"location": "Location", "businessId": "Customer Business Name", "businessName": "Customer Business Name", "legalName": "Customer Legal Name, if different from Business Name", "sfOpportunityId": "Salesforce Opp ID", "customerRelationship": "Customer Relationship", "rfp": "RFP", "rfpDueDate": "RFP Due Date", "pricingLevel": "Pricing Level", "printAssessment": "Print Assessment", "coreItsNetworkScan": "CORE ITS Network Scan", "minimumCommitment": "Minimum Commitment", "paymentMode": "Payment Mode", "paymentJustification": "Payment Justification", "oneMultipleSuppliers": "One/Multiple Supplier(s)", "implementation": "Implementation", "foreignBuyoutTradeUp": "Foreign Buyout/Trade Up", "msa": "MSA", "proposalDate": "Proposal Date", "addressLine1": "Address Line 1", "addressLine2": "Address Line 2", "addressLine3": "Address Line 3", "city": "City", "state": "State", "zipCode": "Zip Code", "country": "Country", "contactName": "Contact Name", "contactEmail": "Contact Email", "contactPhone": "Contact Phone", "contactTitle": "Contact Title", "customerNotes": "Customer Notes", "province": "Province", "postalCode": "Postal Code", "phone": "Phone", "customerWebsite": "Customer Website", "globalAgreement": "Global Agreement", "branch": "Branch", "salesRep": "Sales Rep", "salesManager": "Sales Manager", "regionalLeader": "Regional Leader", "salesChannel": "Sales Channel", "portfolio": "Portfolio", "typeOfContract": "Type of Contract", "leaseTerm": "Lease Term", "billingPeriod": "Billing Period", "salesRepOid": "Sales Rep", "salesManagerOid": "Sales Manager", "regionalLeaderOid": "Regional Leader", "porfolioId": "Portfolio", "notaManagerError": "Selected user is not a manager", "managerSearchLabel": "Name or email or ID of the manager", "managerSearchLabelRl": "Name or email or ID of the regional leader", "searchHeader": "Search Manager"}, "equipments": {"placeholder": "Equipment Information", "coming_soon": "This section will be implemented soon"}, "worksheets": {"title": "Worksheets", "view_toggle_label": "Condensed View", "summary_section_title": "Summary", "total_summary_title": "Total Summary", "hardware_summary_title": "Hardware Summary", "solutions_summary_title": "Solutions Summary", "proposal_type_primary": "Primary Proposal", "proposal_type_optional": "Optional Proposal", "summary_total_selling_price": "Total Selling Price", "summary_total_msrp": "Total MSRP", "summary_percent_of_msrp": "% of MSRP", "hardware_title": "Hardware", "solutions_title_condensed": "Solution Categories", "solutions_title_expanded": "Solution Categories", "main_unit_title": "Main Unit #{index}", "accessory_title": "Accessory #{index}", "solution_item_title": "Solution Item #{index}", "summary_for_main_unit_title": "Summary for Main Unit #{index}", "total_msrp_label": "Total MSRP:", "total_selling_price_label": "Total Selling Price:", "avg_percent_msrp_label": "Average % of MSRP:", "columns": {"item": "<PERSON><PERSON>", "item_number": "Item No.", "dsd_qty": "DSD Qty", "dealer_its": "Dealer ITs", "selling_price": "Selling <PERSON>", "msrp": "MSRP", "percent_msrp": "% MSRP", "actions": "Actions"}, "messages": {"no_items_added": "No {item_type} added yet.", "no_locations_found": "No locations found."}, "fields": {"product_name": {"label": "Product Name"}, "solution_category_name": {"label": "Solution Category Name"}, "item_number": {"label": "Item Number"}, "dsd_quantity": {"label": "DSD Quantity"}, "dealer_its_quantity": {"label": "Dealer ITs Quantity"}, "msrp": {"label": "MSRP"}, "percent_of_msrp": {"label": "% of MSRP"}, "request_selling_price": {"label": "Request Selling Price"}}, "buttons": {"add_accessory": "Add Accessory", "add_solution_item": "Add Solution Item", "remove_main_item": "Remove this item", "remove_main_unit": "Remove Main Unit", "remove": "Remove", "add_main_unit_typed": "Add {unit_type}", "remove_accessory": "Remove Accessory", "add_item": "Add {item_type}"}, "tabs": {"all": "All", "hardware": "Hardware", "solutions": "Solutions"}}, "success_message": "Sales Request Form saved successfully.", "error_message": "Failed to save Sales Request Form. Please try again later.", "errors": {"no_portfolio_selected": "Please Select the Portfolio option from Customer Details Tab to proceed with the Service Form"}}, "worksheets_management": {"title": "Worksheets Management", "tabs": {"hardware": "Hardware", "software": "Software"}, "table": {"header": {"product_name": "Product Name", "item_number": "Item Number", "display_name": "Display Name", "model_name": "Model Name", "description": "Description", "dsd_quantity": "DSD Quantity", "dealer_quantity": "Dealer ITs Quantity", "selling_price": "Selling <PERSON>", "msrp": "MSRP", "wholesale_cost": "Wholesale Cost", "percent_msrp": "% of MSRP"}, "sub_products": "Sub Products"}, "button": {"new_worksheet": "New Worksheet", "add_first": "Add First {category} Item"}, "no_data": "No {category} items found", "loading_sub_products": "Loading sub-products...", "no_sub_products": "No sub-products available", "dialog": {"add_charges": "Add Charges", "edit_charges": "Edit Charges", "portfolio": "Portfolio", "service_value_pack": "Service Value Pack", "msrp": "MSRP", "bw": "B&W", "colour": "Colour", "iprc": "Extra Long (IPRC)", "minimum_base_amount": "Minimum Base $Amt", "minimum_base_volume": "Minimum Base Volume"}}, "validation_error": "Please fill in all required fields.", "success_message": "Sales Request Form submitted successfully.", "error_message": "Failed to submit Sales Request Form. Please try again later."}, "component": {"usermenu": {"button": {"signin": {"label": "Sign In"}, "signout": {"label": "Sign Out"}, "english": {"label": "English"}, "french": {"label": "French"}, "light": {"label": "Light"}, "dark": {"label": "Dark"}}}, "appsnackbar": {"button": {"close": {"label": "Close"}}}, "appmessage": {"refresh": "Please try clicking the <strong>Refresh</strong> button in your browser. If this problem continues, please contact I.T. support.", "support": "If this problem continues, please contact I.T. support.", "button": {"refresh": {"label": "Refresh"}, "close": {"label": "Close"}}}, "appheader": {"button": {"home": {"label": "Home"}, "role_mgmt": {"label": "Role Management"}, "configuration": {"label": "Configuration"}, "user_access": {"label": "User Access"}, "sales_request_listing": {"label": "Sales Request List"}, "price_desk_listing": {"label": "Price Desk List"}, "service_desk_listing": {"label": "Service Desk List"}}}, "appfooter": {"maintained": "Maintained by CCI IT", "version": "Version"}}, "common": {"actions": "Actions", "submit": "Submit", "next": "Next", "cancel": "Cancel", "add": "Add", "update": "Update"}, "status": {"STATUS_DRAFT": "Draft", "STATUS_SERVICE_FORM_PENDING": "Service Form Pending", "STATUS_MANAGER_APPROVAL_PENDING": "Waiting for Manager <PERSON><PERSON><PERSON><PERSON>", "STATUS_RL_APPROVAL_PENDING": "Waiting for RL Approval", "STATUS_PD_APPROVAL_PENDING": "Waiting for Price Desk Approval", "STATUS_SERVICE_APPROVAL_PENDING": "Waiting for Service Desk Approval", "STATUS_APPROVED": "Approved", "STATUS_SERVICE_FORM_APPROVED": "Service Form Approved"}}