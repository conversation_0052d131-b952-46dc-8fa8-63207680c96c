<script setup lang="ts">
import { defineProps, computed } from 'vue';

const props = defineProps({
  modelValue: {
    type: Object,
    required: true,
    default: () => ({})
  }
});

// Use a computed property with default values
const displayData = computed(() => ({
  request_date: props.modelValue.request_date || '2025-05-22',
  sales_representative_name: props.modelValue.sales_representative_name || '<PERSON>',
  sales_manager_name: props.modelValue.sales_manager_name || '<PERSON>',
  customer_business_name: props.modelValue.customer_business_name || 'Acme Corporation',
  customer_legal_name: props.modelValue.customer_legal_name || 'Acme Corporation Inc.',
  address1: props.modelValue.address1 || '123 Business Ave',
  address2_3: props.modelValue.address2_3 || 'Suite 100',
  city_province_postal: props.modelValue.city_province_postal || 'Toronto, ON M1M 1M1'
}));
</script>

<template>
  <v-card class="mb-4">
    <v-expansion-panels>
      <v-expansion-panel>
        <v-expansion-panel-title>
          <v-row no-gutters>
            <v-col class="text-h6">Basic Request Information</v-col>
          </v-row>
        </v-expansion-panel-title>
        <v-expansion-panel-text>
          <v-card-text class="pt-4">
            <v-row>
              <v-col cols="12" md="6">
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Request Date</span>
                  <span class="text-body-1">{{ displayData.request_date }}</span>
                </div>
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Sales Representative</span>
                  <span class="text-body-1">{{ displayData.sales_representative_name }}</span>
                </div>
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Sales Manager</span>
                  <span class="text-body-1">{{ displayData.sales_manager_name }}</span>
                </div>
              </v-col>
              <v-col cols="12" md="6">
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Customer Business Name</span>
                  <span class="text-body-1">{{ displayData.customer_business_name }}</span>
                </div>
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Customer Legal Name</span>
                  <span class="text-body-1">{{ displayData.customer_legal_name }}</span>
                </div>
              </v-col>
              <v-col cols="12">
                <div class="d-flex flex-column mb-4">
                  <span class="text-caption text-grey">Address</span>
                  <div class="d-flex flex-column">
                    <span class="text-body-1">{{ displayData.address1 }}</span>
                    <span class="text-body-1" v-if="displayData.address2_3">{{ displayData.address2_3 }}</span>
                    <span class="text-body-1">{{ displayData.city_province_postal }}</span>
                  </div>
                </div>
              </v-col>
            </v-row>
          </v-card-text>
        </v-expansion-panel-text>
      </v-expansion-panel>
    </v-expansion-panels>
  </v-card>
</template>

<style scoped>
.text-caption {
  line-height: 1.2;
  margin-bottom: 2px;
  color: rgba(0, 0, 0, 0.6);
  font-size: 0.75rem;
  font-weight: 400;
}

.text-body-1 {
  line-height: 1.5;
  font-size: 1rem;
  color: rgba(0, 0, 0, 0.87);
}
</style>
