<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  modelValue: { // Corresponds to formData.current_equipment_details.equipment_list
    type: String,
    required: true,
    default: '',
  },
});

const emit = defineEmits(['update:modelValue']);

const localEquipmentList = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
});

</script>

<template>
  <v-card class="mb-4">
    <v-card-title>Current Equipment Details</v-card-title>
    <v-alert density="compact" type="info" variant="tonal" style="margin: 0px 16px;">
        Please Provide Serial Numbers When Replacing Canon Machines.
      </v-alert>
    <v-card-text>
      <v-textarea
        v-model="localEquipmentList"
        label=""
        rows="4"
        auto-grow
        variant="outlined"
        class="mb-2"
         :maxlength="1000"
        counter
        persistent-counter
      />

    </v-card-text>
  </v-card>
</template>

<style scoped>
/* Component-specific styles */
</style>
